import unittest
import sys
import os
from src.post_publish_file_based import _parse_args
 
class TestPostPublishWorkflow(unittest.TestCase):
    def test_parse_args(self):
        test_args_with_parameters = [
            'program_name',
            '--channel', 'test_channel',
            '--runId', 'test_run_id',
            '--inputS3Path', 's3://test_path',
            '--labels', '["label1", "label2"]',
            '--parameters', '{"param1": "value1"}'
        ]
        sys.argv = test_args_with_parameters
        args = _parse_args()
        assert args.channel == 'test_channel'
        assert args.runId == 'test_run_id'
        assert args.inputS3Path == 's3://test_path'
        assert args.labels == '["label1", "label2"]'
        assert args.parameters == '{"param1": "value1"}'

        test_args_without_parameters = [
            'program_name',
            '--channel', 'test_channel',
            '--runId', 'test_run_id',
            '--inputS3Path', 's3://test_path',
            '--labels', '["label1", "label2"]'
        ]
        sys.argv = test_args_without_parameters
        args = _parse_args()
        assert args.channel == 'test_channel'
        assert args.runId == 'test_run_id'
        assert args.inputS3Path == 's3://test_path'
        assert args.labels == '["label1", "label2"]'
        assert args.parameters == None
