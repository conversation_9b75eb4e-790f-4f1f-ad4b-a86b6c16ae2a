import gzip
import json
import os
import shutil
import unittest

import duckdb
import numpy as np
import pandas as pd

from src.common.config import setup_config
from src.trip_advisor import TAPartnerWorkflow, get_brand_config
from tests import cleanup_partner_data


class TestTAPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/test_input.csv'
        output_file = '/workspace/input/bids/test_input.csv.gz'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/trip_advisor/data/test_input.csv', input_file)
        with open(input_file, "rb") as f_in:
            with gzip.open(output_file, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.system(command=f'ls /workspace/input/bids/')

        run_id = 'run_id'
        channel = 'trip-advisor'
        input_s3_path = 'input_s3_path'
        brand = 'cheaptickets'
        self.config = setup_config(run_id=run_id, channel=channel, input_s3_path=input_s3_path, brand=brand)
        self.wf = TAPartnerWorkflow(self.config, '2024-10-30-07-05-16')
        self.brand_config = get_brand_config(self.config.run_conf.brand)

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        result = self.wf.duck_con.sql(
            f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_working_table} WHERE submission_type == 'AUDIENCE_MULTIPLIER'")
        bids_table_data = result.to_pandas()
        expected_audience_data = {
            "submission_type": "AUDIENCE_MULTIPLIER",
            "posa": "US",
            "device": "DESKTOP",
            "hotel_id": None,
            "bid_value_usd_cents": None,
            "audience_id": 14688,
            "hotel_group": None,
            "multiplier_type": None,
            "multiplier_lower": None,
            "multiplier_upper": None,
            "multiplier_value": 1.45,
            "metadata": None,
            "experiment_name": None,
            "experiment_bucket": None
        }

        expected = pd.DataFrame([expected_audience_data])
        results = bids_table_data
        assert results.equals(expected)

    def test_transform_bids(self):
        self.wf._transform_bids(self.brand_config)

        # Base bid
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_base_bids")
        base_bids_table_data = result.to_pandas()
        expected_base_bid_transformed_data = [
            {
                "Client ID": 5,
                "US-DMeta_BID": None,
                "US-DMeta_BIN": "BMP",
                "US-Mobile_BID": None,
                "US-Mobile_BIN": None
            },
            {
                "Client ID": 8,
                "US-DMeta_BID": 195.0,
                "US-DMeta_BIN": "PNB",
                "US-Mobile_BID": None,
                "US-Mobile_BIN": None
            }
        ]
        expected = pd.DataFrame(expected_base_bid_transformed_data).sort_values(by='Client ID', ignore_index=True)
        results = base_bids_table_data.sort_values(by='Client ID', ignore_index=True)
        assert results.equals(expected)

        # Audience Multipliers
        result = self.wf.duck_con.sql(
            f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_audience_multipliers")
        audience_mult_table_data = result.to_pandas()
        expected_audience_mult_transformed_data = {
            'AUDIENCE ID': 14688,
            'US-DMeta': 1.45,
            'US-Mobile': np.nan
        }

        expected = pd.DataFrame([expected_audience_mult_transformed_data]).sort_values(by='AUDIENCE ID', ignore_index=True)
        results = audience_mult_table_data.sort_values(by='AUDIENCE ID', ignore_index=True)
        assert results.equals(expected)

        # PNB Multipliers
        result = self.wf.duck_con.sql(
            f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_pnb_multipliers")
        pnb_mult_table_data = result.to_pandas()
        print(pnb_mult_table_data)
        expected_pnb_mult_transformed_data = [
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'tod', 'rangeStart': '0', 'rangeEnd': '5',
             'multiplier': 0.620},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'tod', 'rangeStart': '6', 'rangeEnd': '11',
             'multiplier': 1.555},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'tod', 'rangeStart': '12', 'rangeEnd': '17',
             'multiplier': 0.800},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'tod', 'rangeStart': '18', 'rangeEnd': '23',
             'multiplier': 0.800},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'dow', 'rangeStart': '1', 'rangeEnd': '2',
             'multiplier': 1.421},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'dow', 'rangeStart': '3', 'rangeEnd': '4',
             'multiplier': 1.000},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'dow', 'rangeStart': '5', 'rangeEnd': '7',
             'multiplier': 1.200},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'cid', 'rangeStart': '8-10-21', 'rangeEnd': '8-15-21',
             'multiplier': 0.667},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'cid', 'rangeStart': '10-12-22', 'rangeEnd': '10-13-22',
             'multiplier': 1.200},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'los', 'rangeStart': '1', 'rangeEnd': '2',
             'multiplier': 0.667},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'los', 'rangeStart': '5', 'rangeEnd': None,
             'multiplier': 0.200},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'guests', 'rangeStart': '1', 'rangeEnd': '1',
             'multiplier': 0.667},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'guests', 'rangeStart': '3', 'rangeEnd': '7',
             'multiplier': 1.200},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'dta', 'rangeStart': '0', 'rangeEnd': '0',
             'multiplier': 3.500},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': 'dta', 'rangeStart': '1', 'rangeEnd': '2',
             'multiplier': 3.000},
            {'silo': 'US-DMeta', 'bucket': 'PNB5', 'bidLever': 'dta', 'rangeStart': '13', 'rangeEnd': '30',
             'multiplier': 1.600},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': None, 'rangeStart': '1', 'rangeEnd': '1',
             'multiplier': 0.667},
            {'silo': 'US-DMeta', 'bucket': 'PNB', 'bidLever': None, 'rangeStart': '1', 'rangeEnd': '1',
             'multiplier': 1.000}
        ]

        expected = pd.DataFrame(expected_pnb_mult_transformed_data).sort_values(by=['multiplier', 'bidLever', 'rangeStart'], ignore_index=True)
        results = pnb_mult_table_data.sort_values(by=['multiplier', 'bidLever', 'rangeStart'], ignore_index=True)
        assert results.equals(expected)

    def test_write_bid_files(self):
        self.wf._write_bid_files()

        gen_path = '/workspace/bids'
        test_path = 'tests/trip_advisor/data/'
        base_bid_file = 'CHEAPTICKETS_hotels_partial_2024-10-30-07-05-16.csv'
        audience_mult_file = 'CHEAPTICKETS_audience_multipliers_2024-10-30-07-05-16.csv'
        pnb_mult_file = 'CHEAPTICKETS_pnb_multipliers_2024-10-30-07-05-16.csv'

        con = duckdb.connect(database=':memory:')

        test_base_bid_data = con.read_csv(test_path + base_bid_file).df()
        gen_base_bid_data = con.read_csv(
            f"{gen_path}/{self.config.run_conf.brand.upper()}/base_bids/{base_bid_file}").df()

        expected = test_base_bid_data.sort_values(by='Client ID', ignore_index=True)
        results = gen_base_bid_data.sort_values(by='Client ID', ignore_index=True)
        assert results.equals(expected)

        test_audience_mult_data = con.read_csv(test_path + audience_mult_file).df()
        gen_audience_mult_data = con.read_csv(
            f"{gen_path}/{self.config.run_conf.brand.upper()}/audience_multipliers/{audience_mult_file}").df()

        expected = test_audience_mult_data.sort_values(by='AUDIENCE ID', ignore_index=True)
        results = gen_audience_mult_data.sort_values(by='AUDIENCE ID', ignore_index=True)
        assert results.equals(expected)

        test_pnb_mult_data = con.read_csv(test_path + pnb_mult_file).df()
        gen_pnb_mult_data = con.read_csv(
            f"{gen_path}/{self.config.run_conf.brand.upper()}/pnb_multipliers/{pnb_mult_file}").df()

        expected = test_pnb_mult_data.sort_values(by=['multiplier', 'bidLever', 'rangeStart'], ignore_index=True)
        results = gen_pnb_mult_data.sort_values(by=['multiplier', 'bidLever', 'rangeStart'], ignore_index=True)
        assert results.equals(expected)

    # def test_write_ta_binaries(self):
    #     from marketing_pb.entity.v1.entity_pb2 import MarketingEntity
    #     from google.protobuf.internal.decoder import _DecodeVarint32  # noqa: WPS450
    #     from google.protobuf.json_format import MessageToJson
    #     os.makedirs('/workspace/bids/CHEAPTICKETS/inventory/', exist_ok=True)
    #     shutil.copy('/app/tests/trip_advisor/data/tripadvisor_cheaptickets_inventory_2024-10-30-07-05-16.tsv', '/workspace/bids/CHEAPTICKETS/inventory/tripadvisor_cheaptickets_inventory_2024-10-30-07-05-16.tsv')
    #     self.wf._write_publisher_binary(self.brand_config)
    #     gen_path = '/workspace/outputs/entities.bin'
    #     entitites = []
    #     with open(gen_path, 'rb') as f:
    #         while True:
    #             buffer = f.read(64 * 8 * 100000)
    #             if not buffer:
    #                 break
    #             n = 0
    #             while n < len(buffer):
    #                 msg_len, new_pos = _DecodeVarint32(buffer, n)
    #                 if msg_len > len(buffer) - n:
    #                     break
    #                 n = new_pos
    #                 msg_buf = buffer[n:n + msg_len]
    #                 n += msg_len
    #                 f.seek(f.tell() + n)
    #                 message = MarketingEntity()
    #                 message.ParseFromString(msg_buf)
    #                 json_with_defaults = MessageToJson(message, always_print_fields_with_no_presence=True)
    #                 entitites.append(json_with_defaults)
    #     with open('/app/tests/trip_advisor/data/entities.json') as f:
    #         expected_proto_as_json = json.load(f)
    #         assert expected_proto_as_json == [json.loads(item) for item in entitites]
