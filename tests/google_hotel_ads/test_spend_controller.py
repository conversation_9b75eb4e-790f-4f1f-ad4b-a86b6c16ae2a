import time
import unittest
from unittest.mock import patch

from spend_controller import resolve_controller_trigger_state

class MockRedis:
    instance = None
    data: dict
    
    def __new__(cls, *args, **kwargs):
        if cls.instance is None:
            print("Mocked Redis used")
            cls.data = {}
            cls.instance = super().__new__(cls)
        return cls.instance

    def __init__(self, host, port):
        pass

    def set(self, name, value, ex=0):
        self.data = {name: value}
    
    def get(self, name):
        results =  self.data.get(name)
        if not results:
            return {}
        return results
    
    
class TestSpendController(unittest.TestCase):

    @patch('redis.Redis', new=MockRedis)
    def test_resolve_controller_trigger_state(self):
        # Init run
        time.sleep(5)
        if resolve_controller_trigger_state('hcom-apac'):
            print("Init run should not run")
            assert False
        
        # Duplicate same run
        time.sleep(5)
        if resolve_controller_trigger_state('hcom-apac'):
            print("Duplicate same run should not run")
            assert False
        
        # Na run
        time.sleep(5)
        if resolve_controller_trigger_state('hcom-emea'):
            print("Init run should not run")
            assert False
        
        # expedia-apac should run
        time.sleep(5)
        if resolve_controller_trigger_state('expedia-apac'):
            print("Spend controller should run")
            assert True

        # expedia-na should run
        time.sleep(5)
        if resolve_controller_trigger_state('expedia-emea'):
            print("Spend controller should run")
            assert True
