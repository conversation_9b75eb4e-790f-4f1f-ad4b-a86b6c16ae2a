import csv
import shutil
import unittest
import os

import duckdb

from src.google_hotel_ads import AdGroup, Campaign, Generator  # Replace with the correct import path


class TestGoogleHotelAds(unittest.TestCase):

    def setUp(self):
        con = duckdb.connect('/tmp/report.db')
        con.execute("CREATE OR REPLACE SEQUENCE negative_id_generator START WITH -1 INCREMENT -1")
        # Mock DuckDB connection and cursor
        self.mock_cur = con.cursor()
        self.mock_run_id = 'test'
        self.mock_account_id = '**********'

        # Instantiate the AdGroup class with mocked dependencies
        self.gha = Generator(self.mock_account_id, self.mock_run_id, self.mock_cur)
        os.makedirs('/workspace/inputs/', exist_ok=True)
        os.system(command='cp -r /app/tests/google_hotel_ads/data/inputs/ /workspace/')
        self.generate_bids()

    def tearDown(self):
        self.mock_cur.close()

    def generate_bids(self):
        self.gha.generate_bids()

    def test_user_country_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_user_country_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 1

    def test_booking_window_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_booking_window_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 4

    def test_date_type_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_date_type_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 0

    def test_day_of_week_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_day_of_week_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 8

    def test_device_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_device_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 0

    def test_hotel_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_hotel_bids.csv'
        removed_bids = 0
        create_bids = 0
        with open(file_path, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if row.get('google_ads_entity.ad_group_criterion.status') == 'REMOVED':
                    removed_bids += 1
                else:
                    create_bids += 1
        assert removed_bids == 4
        assert create_bids == 5

    def test_length_of_stay_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_length_of_stay_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 2

    def test_new_ad_groups(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_new_ad_groups.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 1

    def test_new_ad_group_ads(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_new_ad_group_ads.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 1
        
        
    def test_campaign_criterion_audience_targeting(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_campaign_criterion_audience_targeting.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 2
        
    def test_campaign_bids(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_campaign_bids.csv'
        bids = 0
        with open(file_path, 'r') as file:
            # removing the header line
            bids = sum(1 for line in file) - 1
        assert bids == 2
        
    def test_parent_ad_group_criterions(self):
        file_path = f'/workspace/bids/{self.mock_account_id}_parent_ad_group_criterions.csv'
        subdivision_bids = 0
        unit_bids = 0
        with open(file_path, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if row.get('google_ads_entity.listing_group_parent_criterion.listing_group.type') == 'UNIT':
                    unit_bids += 1
                else:
                    subdivision_bids += 1
        assert subdivision_bids == 1
        assert unit_bids == 1

if __name__ == '__main__':
    unittest.main()
