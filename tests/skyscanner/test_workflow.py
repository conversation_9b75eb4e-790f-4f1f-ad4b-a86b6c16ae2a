import gzip
import json
import os
import shutil
import unittest
from datetime import datetime
from unittest.mock import patch, MagicMock

import pandas as pd

from src.common.config import setup_config, setup_sftp
from src.skyscanner import SkyscannerWorkflow
from tests import cleanup_partner_data


class TestSkyscannerPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/test_input.csv'
        output_file = '/workspace/input/bids/test_input.csv.gz'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/skyscanner/data/test_values.csv', input_file)
        with open(input_file, "rb") as f_in:
            with gzip.open(output_file, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.system('ls /workspace/input/bids/')
        run_id = 'run_id'
        channel = 'skyscanner'
        s3_path = ''
        config = setup_config(run_id=run_id, channel=channel, input_s3_path=s3_path, brand='')
        sftp_config = setup_sftp();
        self.wf = SkyscannerWorkflow(config, sftp_config)

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_01_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_working_table}")
        bids_table_data = result.to_pandas()
        test_data = """
        [
            {
                "submission_type": "BASE_BID",
                "posa": "AU",
                "device": "DESKTOP",
                "hotel_id": 116653179.0,
                "bid_value_usd_cents": 304.0,
                "hotel_group": "PNB3",
                "audience_id": null,
                "multiplier_type": null,
                "multiplier_lower": null,
                "multiplier_upper": null,
                "multiplier_value": null,
                "metadata": "{\\"model\\": {\\"BidClipping_AU_3.3.1_WOTIF_desktop\\": {\\"previous_base_bid\\": 1.6717113564330854, \\"action\\": \\"None\\", \\"base_bid\\": 1.6717113564330854}, \\"currency_conversion\\": {\\"factor\\": 100.0}, \\"BidAdjustor_AU_3.7.3\\": {\\"previous_base_bid\\": 167.17113564330853, \\"bid_up_factor\\": 1.05, \\"bid_down_factor\\": 0.6, \\"bid_adjustor\\": \\"VPCBidAdjustor\\", \\"leader_assignment\\": \\"missing\\"}}, \\"vulcan\\": {\\"phase\\": \\"\\", \\"experiment_name\\": \\"NaN\\", \\"hotel_group_name\\": \\"international\\", \\"eg_property_id\\": \\"116653179\\", \\"frozen\\": \\"false\\", \\"hotel_group\\": \\"PNB3\\", \\"frozen_reason\\": null, \\"hcom_property_id\\": \\"3733901728\\", \\"meta_data\\": null, \\"expedia_property_id\\": \\"116653179\\", \\"maximum\\": null, \\"experiment_bin\\": \\"NaN\\", \\"minimum\\": null}}",
                "experiment_name": null,
                "experiment_bucket": null
            },
            {
                "submission_type": "BASE_BID",
                "posa": "AU",
                "device": "DESKTOP",
                "hotel_id": 2992985.0,
                "bid_value_usd_cents": 551.0,
                "hotel_group": "PNB3",
                "audience_id": null,
                "multiplier_type": null,
                "multiplier_lower": null,
                "multiplier_upper": null,
                "multiplier_value": null,
                "metadata": "{\\"model\\": {\\"BidClipping_AU_3.3.1_WOTIF_desktop\\": {\\"previous_base_bid\\": 3.038430185349642, \\"action\\": \\"None\\", \\"base_bid\\": 3.038430185349642}, \\"currency_conversion\\": {\\"factor\\": 100.0}, \\"BidAdjustor_AU_3.7.3\\": {\\"previous_base_bid\\": 303.8430185349642, \\"bid_up_factor\\": 1.05, \\"bid_down_factor\\": 0.6, \\"bid_adjustor\\": \\"VPCBidAdjustor\\", \\"leader_assignment\\": \\"missing\\"}}, \\"vulcan\\": {\\"phase\\": \\"\\", \\"experiment_name\\": \\"NaN\\", \\"hotel_group_name\\": \\"international\\", \\"eg_property_id\\": \\"2992985\\", \\"frozen\\": \\"false\\", \\"hotel_group\\": \\"PNB3\\", \\"frozen_reason\\": null, \\"hcom_property_id\\": \\"332692\\", \\"meta_data\\": null, \\"expedia_property_id\\": \\"2992985\\", \\"maximum\\": null, \\"experiment_bin\\": \\"NaN\\", \\"minimum\\": null}}",
                "experiment_name": null,
                "experiment_bucket": null
            },
            {
                "submission_type": "PNB_MULTIPLIER",
                "posa": "AU",
                "device": "DESKTOP",
                "hotel_id": null,
                "bid_value_usd_cents": null,
                "hotel_group": "PNB3",
                "audience_id": null,
                "multiplier_type": "TIME_OF_DAY",
                "multiplier_lower": 18.0,
                "multiplier_upper": 23.0,
                "multiplier_value": 1.0,
                "metadata": "{\\"model\\": {}, \\"vulcan\\": {\\"phase\\": \\"PostOverride\\", \\"hotel_group_name\\": \\"international\\", \\"experiment_name\\": \\"NaN\\", \\"experiment_bin\\": \\"NaN\\"}}",
                "experiment_name": null,
                "experiment_bucket": null
            }
        ]
        """
        test_data_df = pd.DataFrame(json.loads(test_data))

        # Ensure matching data types
        test_data_df = test_data_df.astype(bids_table_data.dtypes.to_dict())

        pd.testing.assert_frame_equal(test_data_df, bids_table_data)


    def test_02_transform_bids(self):
        self.wf._transform_bids()
        result_basebid = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_base_bids")
        base_bids_data = """
            [
                {
                    "Hotel ID":2992985,
                    "AU-Desktop":551,
                    "AU-Desktop-Group":"PNB3"
                },
                {
                    "Hotel ID":116653179,
                    "AU-Desktop":304,
                    "AU-Desktop-Group":"PNB3"
                }
            ]
            """
        base_bids_data_df = pd.DataFrame(json.loads(base_bids_data))
        pd.testing.assert_frame_equal(base_bids_data_df,result_basebid.to_pandas())

        result_multiplier = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_multipliers")
        multipliers_data = """
            [
                {
                    "Multiplier Type":"TIME_OF_DAY",
                    "Multiplier Start":18,
                    "Multiplier End":23,
                    "Multiplier Value":1,
                    "Campaign":"AU-Desktop",
                    "Group":"PNB3"
                }
            ]
        """
        multipliers_data_df = pd.DataFrame(json.loads(multipliers_data))
        pd.testing.assert_frame_equal(multipliers_data_df,result_multiplier.to_pandas())

    def test_03_write_bid_files(self):
        now_str = datetime.now().strftime("%d%m%Y")
        self.wf._write_bid_files(now_str)
        base_bids_file_path = os.path.join(self.wf.config.sys_conf.bids_out.rstrip('/'), 'transformed-bids/base-bids/')        # Count the number of files in the directory
        base_bids_file_count = len(
            [f for f in os.listdir(base_bids_file_path) if os.path.isfile(os.path.join(base_bids_file_path, f))])
        # assert the number of files
        self.assertEqual(base_bids_file_count, 1)

        multipliers_file_path = os.path.join(self.wf.config.sys_conf.bids_out.rstrip('/'), 'transformed-bids/bid-multipliers/')        # Count the number of files in the directory
        multipliers_file_path = len(
            [f for f in os.listdir(multipliers_file_path) if os.path.isfile(os.path.join(multipliers_file_path, f))])
        # assert the number of files
        self.assertEqual(multipliers_file_path, 1)

    @patch('paramiko.RSAKey.from_private_key_file')
    @patch('paramiko.SSHClient')
    def test_04_upload_to_sftp(self, mock_rsa_key, mock_ssh_client):
        # Mock the RSAKey.from_private_key_file method
        mock_key = MagicMock()
        mock_rsa_key.return_value = mock_key

        mock_ssh_instance = MagicMock()
        mock_ssh_client.return_value = mock_ssh_instance
        mock_sftp = MagicMock()
        mock_ssh_instance.open_sftp.return_value = mock_sftp
        now_str = datetime.now().strftime("%d%m%Y")
        self.wf._write_bid_files(now_str)
        self.wf._upload_to_sftp()
