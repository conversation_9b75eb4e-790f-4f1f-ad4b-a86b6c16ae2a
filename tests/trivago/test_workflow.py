import gzip
import os
import shutil
import unittest

import duckdb
import numpy as np
import pandas as pd

from src.common.config import setup_config
from src.trivago import TVGOPartnerWorkflow, get_brand_config
from tests import cleanup_partner_data


class TestTVGOPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/test_input.csv'
        output_file = '/workspace/input/bids/test_input.csv.gz'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/trivago/data/test_input.csv', input_file)
        with open(input_file, "rb") as f_in:
            with gzip.open(output_file, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.system('ls /workspace/input/bids/')
        run_id = 'run_id'
        channel = 'trivago'
        brand = 'wotif'
        s3_path = 's3_path'
        config = setup_config(run_id=run_id, channel=channel, input_s3_path=s3_path, brand=brand)
        self.wf = TVGOPartnerWorkflow(config)
        self.brand_config = get_brand_config(config.run_conf.brand)

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_working_table} WHERE submission_type == 'BASE_BID'")
        bids_table_data = result.to_pandas()
        expected_base_bid_data = {
            "submission_type": "BASE_BID",
            "posa": "VN",
            "metadata": "{}",
            "device": "DESKTOP",
            "placement": "HOTEL - CORE SEARCH",
            "experiment_name": None,
            "experiment_bucket": None,
            "hotel_id": ********,
            "bid_value": 1,
            "hotel_group": 0,
            "multiplier_value": np.nan,
            "multiplier_type": None,
            "multiplier_level": None,
        }
        pd.testing.assert_frame_equal(pd.DataFrame([expected_base_bid_data]), bids_table_data)
    def test_validate_bids(self):
        try:
            self.wf._validate_input_bids()
        except Exception as e:
            self.fail(f"_validate_input_bids() raised {e} unexpectedly!")
    def test_transform_bids(self):
        self.wf._transform_bids()
        # Basebids
        result_basebid = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_base_bids")
        expected_base_bid_transform_data = {
            "trivago_entity.base_bid.partner_reference": "********",
            "trivago_entity.base_bid.locale": "VN",
            "trivago_entity.base_bid.cpc1": np.int32(1),
            "trivago_entity.base_bid.adgroup_id": "0",
            "account_id": np.int8(1),
            "trivago_entity.brand": "wotif",
        }
        pd.testing.assert_frame_equal(pd.DataFrame([expected_base_bid_transform_data]),result_basebid.to_pandas())
        # Multipliers
        result_multiplier = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_multipliers")
        expected_multiplier_transform_data = {
            "trivago_entity.multiplier.property_group_id": "0",
            "trivago_entity.multiplier.partner_pos": "IL",
            "trivago_entity.multiplier.dimension": "LOS",
            "trivago_entity.multiplier.breakout": "15",
            "trivago_entity.multiplier.modifier": 1.4,
            "account_id": np.int8(1),
            "trivago_entity.brand": "wotif",
        }
        pd.testing.assert_frame_equal(pd.DataFrame([expected_multiplier_transform_data]),result_multiplier.to_pandas())

    def test_write_bid_files(self):
        now_str = '2024-10-30T07-05-16'
        self.wf._write_bid_files(now_str)
        gen_path = '/workspace/bids/'
        test_path = 'tests/trivago/data/'
        base_bid_file = f'{self.wf.config.run_conf.brand.upper()}_base_bids_second_price_auction_2024-10-30T07-05-16.csv'
        multiplier_file = f'{self.wf.config.run_conf.brand.upper()}_modifiers_second_price_auction_2024-10-30T07-05-16.csv'

        con = duckdb.connect(database=':memory:')

        test_base_bid_data = con.read_csv(test_path + base_bid_file).df()
        gen_base_bid_data = con.read_csv(f'{gen_path}{self.wf.config.run_conf.brand.upper()}/transformed-bids/base-bids/{base_bid_file}').df()

        # assert gen_base_bid_data.equals(test_base_bid_data)
        print(pd.testing.assert_frame_equal(gen_base_bid_data, test_base_bid_data))

        test_multiplier_bid_data = con.read_csv(test_path + multiplier_file).df()
        gen_multiplier_bid_data = con.read_csv(f'{gen_path}{self.wf.config.run_conf.brand.upper()}/transformed-bids/bid-multipliers/{multiplier_file}').df()
        pd.testing.assert_frame_equal(gen_multiplier_bid_data, test_multiplier_bid_data)
