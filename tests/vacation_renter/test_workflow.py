import gzip
import os
import shutil
import unittest

from paramiko.ssh_exception import SSHException

from src.common.config import setup_config, setup_sftp
from src.hometogo import HomeToGoWorkflow

from tests import cleanup_partner_data

class TestVacationRenterPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/test_input.csv'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/vacation_renter/data/test_input.csv', input_file)
        os.system('ls /workspace/input/bids/')
        run_id = 'run_id'
        channel = 'vacation_renter'
        s3_path = ''
        config = setup_config(run_id=run_id, channel=channel, input_s3_path=s3_path, brand='')
        sftp_config = setup_sftp();
        self.wf = HomeToGoWorkflow(config, sftp_config);

        os.environ['PUBLISH'] = 'False'

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        self.assertEquals(len(self.wf.input_files), 1)

    def test_upload_to_sftp(self):
        self.assertRaises(SSHException, self.wf._upload_to_sftp)