import gzip
import os
import shutil
import unittest
from unittest.mock import patch, MagicMock

from paramiko.ssh_exception import SSHException

from src.common.config import setup_config, setup_sftp
from src.kayak import NonCoreKayakPartnerWorkflow

from tests import cleanup_partner_data

class TestNonCoreKayakPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/US_FSR_Submission_2025_05_19.xlsx'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/kayak/data/US_FSR_Submission_2025_05_19.xlsx', input_file)
        os.system('ls /workspace/input/bids/')
        run_id = 'run_id'
        channel = 'kayak'
        s3_path = ''
        config = setup_config(run_id=run_id, channel=channel, input_s3_path=s3_path, brand='')
        sftp_config = setup_sftp()
        self.wf = NonCoreKayakPartnerWorkflow(config, sftp_config)

        os.environ['PUBLISH'] = 'False'

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        self.assertEquals(len(self.wf.input_files), 1)

    @patch('paramiko.RSAKey.from_private_key')
    @patch('paramiko.SSHClient')
    def test_upload_to_sftp(self, mock_rsa_key, mock_ssh_client):
        mock_key = MagicMock()
        mock_rsa_key.return_value = mock_key

        mock_ssh_instance = MagicMock()
        mock_ssh_client.return_value = mock_ssh_instance
        mock_sftp = MagicMock()
        mock_ssh_instance.open_sftp.return_value = mock_sftp
        self.wf._upload_to_sftp()