import gzip
import os
import shutil
import unittest

import duckdb
import numpy as np
import pandas as pd

from src.common.config import setup_config
from src.kayak.cars import KayakCarsPartnerWorkflow
from tests import cleanup_partner_data


class TestKayakCarsPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        output_dir = '/workspace/input/bids/'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copytree('/app/tests/kayak/data/cars/input/', output_dir, dirs_exist_ok=True)
        os.system(command=f'ls /workspace/input/bids/')

        run_id = 'run_id'
        channel = 'kayak'
        input_s3_path = 'input_s3_path'
        brand = 'CARRENTALS'
        self.config = setup_config(run_id=run_id, channel=channel, input_s3_path=input_s3_path, brand=brand)
        self.config.sys_conf.bids_local_path = '/workspace/input/bids/'
        self.config.sys_conf.bids_out = '/workspace/output/bids/'
        self.wf = KayakCarsPartnerWorkflow(self.config)

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_header(self):
        now_str = '2025-02-25'
        self.wf._build_header(now_str)
        
        expected_header_file_path = os.path.join(
            self.config.sys_conf.bids_out,
            f"0-0-CARRENTALS_US_HEADER_{now_str}_kayak_cars_bob_bids.csv"
        )
        
        assert os.path.exists(expected_header_file_path)
        
        with open(expected_header_file_path, 'r') as f:
            header_content = f.read()
        
        expected_content = (
            "kayak_entity.header.country,kayak_entity.header.mode,kayak_entity.header.hotel_brand,kayak_entity.header.optional_portfolio_id,kayak_entity.header.lob,kayak_entity.header.placement_type,account_id\n"
            "US,partial,,CARRENTALS,LOB_CARS,PLACEMENT_TYPE_CORE,1\n"
        )
        
        assert header_content == expected_content

    def test_write_bid_files(self):
        self.wf._rename_columns_in_all_csv()
        self.wf._copy_files()

        gen_path = '/workspace/output/bids/'
        test_path = 'tests/kayak/data/cars/'
        desktop_bid_file = 'desktop-test-input.csv'
        mobile_bid_file = 'mobile-test-input.csv'

        test_desktop_bid_data = pd.read_csv(test_path + desktop_bid_file)
        gen_desktop_bid_data = pd.read_csv(gen_path + desktop_bid_file)

        assert gen_desktop_bid_data.equals(test_desktop_bid_data)
        
        test_mobile_bid_data = pd.read_csv(test_path + mobile_bid_file)
        gen_mobile_bid_data = pd.read_csv(gen_path + mobile_bid_file)

        assert gen_mobile_bid_data.equals(test_mobile_bid_data)


