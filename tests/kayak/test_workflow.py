import gzip
import os
import shutil
import unittest

import duckdb
import numpy as np
import pandas as pd

from src.common.config import setup_config
from src.kayak import KayakPartnerWorkflow
from tests import cleanup_partner_data


class TestKayakPartnerWorkflow(unittest.TestCase):
    def setUp(self):
        input_file = '/workspace/input/bids/test_input.csv'
        output_file = '/workspace/input/bids/test_input.csv.gz'
        os.makedirs("/workspace/input/bids/", exist_ok=True)
        shutil.copy('/app/tests/kayak/data/test_input.csv', input_file)
        with open(input_file, "rb") as f_in:
            with gzip.open(output_file, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.system(command=f'ls /workspace/input/bids/')

        run_id='run_id'
        channel='kayak'
        input_s3_path='input_s3_path'
        brand='expedia'
        config = setup_config(run_id=run_id, channel=channel, input_s3_path=input_s3_path, brand=brand)
        self.wf = KayakPartnerWorkflow(config)

    @classmethod
    def tearDownClass(cls):
        cleanup_partner_data()

    def test_load_inputs_bids(self):
        self.wf._load_inputs_bids()
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_working_table} WHERE submission_type == 'BASE_BID' AND device == 'DESKTOP'")
        bids_table_data = result.to_pandas()
        expected_audience_data = {
            "submission_type": "BASE_BID",
            "posa": "CA",
            "device": "DESKTOP",
            "hotel_id": "********",
            "bid_value": "0.61",
            "hotel_group": "domestic_expensive",
            "brand": "EXPEDIA",
            "multiplier_type": None,
            "multiplier_level": None,
            "multiplier_value": None,
            "currency_type": "US_DOLLAR",
            "max_cpc": "6.2",
            "metadata": None,
            "experiment_name": None,
            "experiment_bucket": None
        }

        assert pd.DataFrame([expected_audience_data]).equals(bids_table_data), "DataFrame mismatch"

    def test_validate_bids(self):
        try:
            self.wf._validate_input_bids()
        except Exception as e:
            self.fail(f"_validate_input_bids() raised {e} unexpectedly!")

    def test_transform_bids(self):
        self.wf._transform_bids()

        # Desktop bid
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_desktop_bids")
        desktop_bids_table_data = result.to_pandas()
        expected_desktop_bid_transformed_data = [
            {
            "kayak_entity.hotel_bid.portfolio_name": "",
            "kayak_entity.hotel_bid.hotel_id": ********,
            "kayak_entity.hotel_bid.base_bob_cpc": 0.61,
            "kayak_entity.hotel_bid.los_1": 2.1,
            "kayak_entity.hotel_bid.los_2": 2.2,
            "kayak_entity.hotel_bid.los_3": 2.3,
            "kayak_entity.hotel_bid.los_4": 2.4,
            "kayak_entity.hotel_bid.los_8": 2.5,
            "kayak_entity.hotel_bid.los_15": 2.6,
            "kayak_entity.hotel_bid.days_to_arrival_0": 0.5,
            "kayak_entity.hotel_bid.days_to_arrival_1": 0.6,
            "kayak_entity.hotel_bid.days_to_arrival_2": 0.7,
            "kayak_entity.hotel_bid.days_to_arrival_3": 0.8,
            "kayak_entity.hotel_bid.days_to_arrival_4": 0.9,
            "kayak_entity.hotel_bid.days_to_arrival_5": 1.1,
            "kayak_entity.hotel_bid.days_to_arrival_6": 1.2,
            "kayak_entity.hotel_bid.days_to_arrival_7": 1.3,
            "kayak_entity.hotel_bid.days_to_arrival_8": 1.4,
            "kayak_entity.hotel_bid.days_to_arrival_15": 1.5,
            "kayak_entity.hotel_bid.days_to_arrival_30": 1.6,
            "kayak_entity.hotel_bid.saturday_stay_true": 2.7,
            "kayak_entity.hotel_bid.distance_to_destination_short": 1.7,
            "kayak_entity.hotel_bid.distance_to_destination_medium": 1.8,
            "kayak_entity.hotel_bid.distance_to_destination_long": 1.9,
            "kayak_entity.hotel_bid.max_cpc": 6.2,
            "kayak_entity.platform": "FULL_SITE",
            "account_id": "1",
            }
        ]

        assert pd.DataFrame(expected_desktop_bid_transformed_data).equals(desktop_bids_table_data)

        # Mobile bid
        result = self.wf.duck_con.sql(f"SELECT * FROM {self.wf.config.sys_conf.duckdb.bids_final_table}_mobile_bids")
        mobile_bids_table_data = result.to_pandas()
        expected_mobile_bid_transformed_data = [
            {
            "kayak_entity.hotel_bid.portfolio_name": "",
            "kayak_entity.hotel_bid.hotel_id": ********,
            "kayak_entity.hotel_bid.base_bob_cpc": 0.57,
            "kayak_entity.hotel_bid.los_1": 4.6,
            "kayak_entity.hotel_bid.los_2": 4.7,
            "kayak_entity.hotel_bid.los_3": 4.8,
            "kayak_entity.hotel_bid.los_4": 4.9,
            "kayak_entity.hotel_bid.los_8": 5.1,
            "kayak_entity.hotel_bid.los_15": 5.2,
            "kayak_entity.hotel_bid.days_to_arrival_0": 3.1,
            "kayak_entity.hotel_bid.days_to_arrival_1": 3.2,
            "kayak_entity.hotel_bid.days_to_arrival_2": 3.3,
            "kayak_entity.hotel_bid.days_to_arrival_3": 3.4,
            "kayak_entity.hotel_bid.days_to_arrival_4": 3.5,
            "kayak_entity.hotel_bid.days_to_arrival_5": 3.6,
            "kayak_entity.hotel_bid.days_to_arrival_6": 3.7,
            "kayak_entity.hotel_bid.days_to_arrival_7": 3.8,
            "kayak_entity.hotel_bid.days_to_arrival_8": 3.9,
            "kayak_entity.hotel_bid.days_to_arrival_15": 4.1,
            "kayak_entity.hotel_bid.days_to_arrival_30": 4.2,
            "kayak_entity.hotel_bid.saturday_stay_true": 5.3,
            "kayak_entity.hotel_bid.distance_to_destination_short": 4.3,
            "kayak_entity.hotel_bid.distance_to_destination_medium": 4.4,
            "kayak_entity.hotel_bid.distance_to_destination_long": 4.5,
            "kayak_entity.hotel_bid.max_cpc": 6.2,
            "kayak_entity.platform": "MOBILE",
            "account_id": "1",
            }
        ]

        assert pd.DataFrame(expected_mobile_bid_transformed_data).equals(mobile_bids_table_data)

    def test_write_bid_files(self):
        now_str = '2024-10-30-07-05-16'
        self.wf._write_bid_files(now_str)

        gen_path = '/workspace/bids/EXPEDIA/'
        test_path = 'tests/kayak/data/'
        desktop_bid_file = 'Kayak_Hotel_BOB_CA_EXPEDIA_FULL_SITE_v2_2024-10-30-07-05-16.csv'
        mobile_bid_file = 'Kayak_Hotel_BOB_CA_EXPEDIA_MOBILE_v2_2024-10-30-07-05-16.csv'
        header_file = 'Kayak_Hotel_BOB_CA_EXPEDIA_HEADER_2024-10-30-07-05-16.csv'

        con = duckdb.connect(database=':memory:')

        test_desktop_bid_data = con.read_csv(test_path + desktop_bid_file).df()
        gen_desktop_bid_data = con.read_csv(gen_path + desktop_bid_file).df()

        assert(gen_desktop_bid_data.equals(test_desktop_bid_data))
        
        test_mobile_bid_data = con.read_csv(test_path + mobile_bid_file).df()
        gen_mobile_bid_data = con.read_csv(gen_path + mobile_bid_file).df()

        assert gen_mobile_bid_data.equals(test_mobile_bid_data)

        test_header_data = con.read_csv(test_path + header_file).df()
        gen_header_data = con.read_csv(gen_path + header_file).df()

        assert gen_header_data.equals(test_header_data)