import glob
import os
from datetime import datetime

import loguru
import paramiko

from src.common.config import Config, SFTPConfig


class VacationRenterWorkflow:
    config: Config
    input_files: list
    sftpConfig: SFTPConfig

    def __init__(self, config: Config, sftpConfig: SFTPConfig):
        self.config = config
        self.sftpConfig = sftpConfig

    def generate_bids(self):
        self._load_inputs_bids()
        self._upload_to_sftp()

    def _load_inputs_bids(self):
        loguru.logger.info('Loading inputs bids')
        self.input_files = [self.config.sys_conf.bids_local_path + i
                       for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                       if i.endswith(('.csv', '.csv.gz'))]

    def _upload_to_sftp(self):
        loguru.logger.info('Uploading to SFTP')
        ssh_client = paramiko.SSHClient()

        host =  "*************"
        username = "vrbo"
        password = self.sftpConfig.password

        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(host, username=username, password=password)

        loguru.logger.info('connected to sftp')

        sftp = ssh_client.open_sftp()
        current_date = datetime.now().strftime("%B%Y")


        if os.environ.get('PUBLISH') == 'True':
            if len(self.input_files) == 0:
                raise FileNotFoundError("No CSV files found in the specified directory.")
            else:
                file = self.input_files[0]
                filename = f'clusterfeed_{current_date}.csv'
                remote_path = f'/home/<USER>/clusterfeedsubmission/{filename}'
                sftp.put(file, remote_path)

        loguru.logger.info('uploaded to sftp successful')
        sftp.close()
        ssh_client.close()