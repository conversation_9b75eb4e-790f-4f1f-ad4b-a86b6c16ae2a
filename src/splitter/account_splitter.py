import csv
import json
import os
from dataclasses import dataclass
from typing import Dict, TextIO, List


from src.splitter import config


@dataclass
class AccountSplitter:
    account_writer: Dict[str, csv.DictWriter]
    account_resource_writers: Dict[str, csv.DictWriter]
    open_files: Dict[str, TextIO]

    def __init__(self):
        self.account_writer = {}
        self.account_resource_writers = {}
        self.open_files = {}

    def insert(self, row, fields):
        account = row.get(config.ACCOUNT_FIELD_NAME)
        resource = row.get(config.BIDDING_LEVEL).lower()
        resource_fields = self.get_fields_by_resource(resource)
        final_resource = f'{account}-{resource}'
        if account not in self.account_writer:
            os.makedirs(f'{config.SPLITTER_OUTPUT_PATH}/', exist_ok=True)
            account_ofp = open(f'{config.SPLITTER_OUTPUT_PATH}/{account}.tsv', 'w')
            writer = csv.DictWriter(account_ofp, fieldnames=fields, delimiter='\t')
            writer.writeheader()
            self.account_writer[account] = writer
            self.open_files[account] = account_ofp
        if final_resource not in self.account_resource_writers:
            fields = resource_fields if resource_fields else fields
            os.makedirs(f'{config.SPLITTER_OUTPUT_PATH}/{account}/', exist_ok=True)
            ofp = open(f'{config.SPLITTER_OUTPUT_PATH}/{account}/{resource}.tsv', 'w')
            writer = csv.DictWriter(ofp, fieldnames=fields, delimiter='\t')
            writer.writeheader()
            self.account_resource_writers[final_resource] = writer
            self.open_files[final_resource] = ofp

        filtered_row = {field: row.get(field) for field in resource_fields}
        self.account_resource_writers[final_resource].writerow(filtered_row)
        self.account_writer[account].writerow(row)

    def close(self):
        for writer in self.open_files.values():
            writer.close()

    def write_accounts(self):
        os.makedirs('/workspace/tmp/', exist_ok=True)
        with open('/workspace/tmp/accounts.json', 'w') as accounts_file:
            split_accounts = []
            for account_resource in self.account_writer.keys():
                account = account_resource.split('-')[0]
                split_accounts.append({'id': account})
            json.dump(split_accounts, accounts_file)

    def get_fields_by_resource(self, resource):
        if resource == 'hotel':
            return self.hotel_columns()
        if resource == 'group':
            return self.group_columns()
        if resource == 'campaign':
            return self.campaign_criterion_columns()

    @staticmethod
    def hotel_columns() -> List[str]:
        return [
            'HOTEL_ID',
            'CAMPAIGN_ID',
            'CAMPAIGN_NAME',
            'AD_GROUP_ID',
            'GROUP_NAME',
            'GROUP_TYPE',
            'BASE_BID',
            'IS_PAUSED',
        ]

    @staticmethod
    def campaign_criterion_columns() -> List[str]:
        return [
            'CAMPAIGN_ID',
            'CAMPAIGN_NAME',
            'BIDMULTIPLIER_TYPE',
            'BIDMULTIPLIER_LEVEL',
            'BIDMULTIPLIER_VALUE',
        ]

    @staticmethod
    def group_columns() -> List[str]:
        return [
            'CAMPAIGN_ID',
            'CAMPAIGN_NAME',
            'AD_GROUP_ID',
            'GROUP_NAME',
            'GROUP_TYPE',
            'BIDMULTIPLIER_TYPE',
            'BIDMULTIPLIER_LEVEL',
            'BIDMULTIPLIER_VALUE',
        ]