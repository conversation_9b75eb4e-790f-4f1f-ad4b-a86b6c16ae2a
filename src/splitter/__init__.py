import csv
import glob
import gzip
import json
import re
import os
import shutil

from loguru import logger

from src.splitter import config
from src.splitter.account_splitter import AccountSplitter
from src.utils import aws
from pyspark.sql import SparkSession


def run(input_s3_path: str):
    logger.info("starting splitter for input s3: {}".format(input_s3_path))
    aws.s3_sync(
        origin=input_s3_path,
        destination=config.TEMP_INPUT_PATH,
    )
    input_files = glob.iglob(f'{config.TEMP_INPUT_PATH}/*/*.csv.gz')
    splitter = AccountSplitter()
    for input_file in input_files:
        logger.info("converting file {}".format(input_file))
        spark = SparkSession.builder.appName("splitter").getOrCreate()
        df = spark.read.csv(input_file, header=True, inferSchema=True)
        df.coalesce(1) \
            .write.option("delimiter", "\t") \
            .option("header", True) \
            .mode("append") \
            .csv(config.SPARK_TEMP)

    tsv_files = glob.glob(f'{config.SPARK_TEMP}/*.csv')
    for tsv_file in tsv_files:
        with open(tsv_file, 'r') as fp:
            logger.info("splitting file {}".format(tsv_file))
            reader = csv.DictReader(fp, delimiter='\t')
            fields = reader.fieldnames
            for row in reader:
                splitter.insert(row, fields)
    splitter.write_accounts()
    splitter.close()
    logger.info("splitting done...")
