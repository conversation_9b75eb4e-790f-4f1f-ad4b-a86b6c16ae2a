dev:
  paths:
    bids_local: '/workspace/input/bids/'
    bids_out: '/workspace/bids/'
    binary_out: '/workspace/outputs/entities.bin'
  integrations:
    egumi: 'egdp_prod_marketing.metasearch_eg_unified_meta_inventory'
    vulcan_update: 'egdp_prod_marketing.metasearch_vulcan_table_latest_update_log'
  trino:
    host: 'egdp-adhoc-trino.egdp-analytics.aws.away.black'
    port: 8443
    catalog: 'hive'
    http_scheme: 'https'
    schema: 'default'
  duckdb:
    db_file: './local.db'
    bids_working_table: 'bids_working'
    bids_final_table: 'bids_final'
    vulcan_ids: 'vulcan_ids'
    inventory_working: 'inv_working'
    inventory_final: 'inv_final'

prod:
  paths:
    bids_local: '/workspace/input/bids/'
    bids_out: '/workspace/bids/'
    binary_out: '/workspace/outputs/entities.bin'
  integrations:
    egumi: 'marketing.metasearch_eg_unified_meta_inventory'
    vulcan_update: 'marketing.metasearch_vulcan_table_latest_update_log'
  trino:
    host: 'egdl-service-trino.egdp-prod.aws.away.black'
    port: 8443
    catalog: 'hive'
    http_scheme: 'https'
    schema: 'default'
  duckdb:
    db_file: './local.db'
    bids_working_table: 'bids_working'
    bids_final_table: 'bids_final'
    vulcan_ids: 'vulcan_ids'
    inventory_working: 'inv_working'
    inventory_final: 'inv_final'
    
