import argparse
import glob
import json
import os
from pathlib import Path

import requests
from loguru import logger
from pyspark.sql import SparkSession
from pyspark.sql.functions import lit, when, col, current_timestamp, to_timestamp
from pyspark.sql.types import StringType, DoubleType

from src.spend_controller import run_spend_controller
from src.utils import aws, airflow

parser = argparse.ArgumentParser()
parser.add_argument(
    '--channel', type=str, help='marketing channel to run for',
)
parser.add_argument(
    '--runId', type=str, help='The unique id for the run',
)
parser.add_argument(
    '--labels', type=str, help='The labels for the run',
)
parser.add_argument(
    '--inputS3Path', type=str, help='The S3 path for the input files',
)

args = parser.parse_args()
run_id: str = args.runId
channel: str = args.channel
labels = json.loads(args.labels)
ottoman_run = requests.post(
    url=f'{os.getenv("OTTOMAN_API")}/getRuns',
    data=json.dumps(
        {
            'client': 'eg',
            'algorithm': 'mbt',
            'channel': f'MARKETING_CHANNEL_{channel.upper().replace("-", "_")}',
            'labels': [labels[1]],
            'limit': 1
        }),
)

ottoman_run.raise_for_status()
logger.info(ottoman_run.json())
finished_at = ottoman_run.json()[0].get('finished_at')

logger.info(f'Running post publish for runId: {run_id}, channel: {channel}, labels: {labels}')

partner = 'GHA' if channel == 'google-hotel-ads' else 'BING'
brand_region_label = labels[0]
vulcan_run_id = labels[1]
input_s3_path = args.inputS3Path

logger.info(f'Downloading inputs from : {input_s3_path}')

if os.getenv('ENV', 'DEV') != 'DEV':
    aws.s3_sync(
        origin=input_s3_path,
        destination='/workspace/inputs/'
    )

input_files = glob.glob(f"/workspace/inputs/*/*.csv.gz")
outputs_path = "/workspace/outputs/"
parquet_outputs_path = "/workspace/parquet_outputs/"
os.makedirs(outputs_path, exist_ok=True)
spark = SparkSession.builder.appName("post_publish").getOrCreate()

for input_file in input_files:
    logger.info(f'Processing {input_file}')
    df = spark.read.csv(input_file, header=True, inferSchema=True)

    # Rename columns using a mapping dictionary
    rename_mapping = {
        'BRAND': 'brand',
        'ACCOUNT_ID': 'account_id',
        'CAMPAIGN_ID': 'campaign_id',
        'CAMPAIGN_NAME': 'campaign_name',
        'HOTEL_ID': 'hotel_id',
        'HOTEL_GROUP': 'hotel_group',
        'BIDDING_LEVEL': 'bidding_level',
        'BID_STRATEGY_TYPE': 'bid_strategy_type',
        'BIDMULTIPLIER_TYPE': 'multiplier_type',
        'BIDMULTIPLIER_LEVEL': 'multiplier_level',
        'GROUP_NAME': 'adgroup_name',
        'METADATA': 'metadata',
        'EXPERIMENT_NAME': 'experiment_name',
        'EXPERIMENT_BUCKET': 'experiment_bucket',
    }
    df = (df
          .withColumnsRenamed(rename_mapping)
          .withColumn('account_id', col('account_id').cast(StringType()))
          .withColumn('campaign_id', col('campaign_id').cast(StringType()))
          .withColumn('hotel_id', col('hotel_id').cast(StringType()))
          .withColumn('multiplier_level', col('multiplier_level').cast(StringType()))
          )
    df = (df
          .withColumn('submission_type', lit(None).cast(StringType()))
          .withColumn("posa", lit(None).cast(StringType()))
          .withColumn("device", lit(None).cast(StringType()))
          .withColumn("placement", lit(None).cast(StringType()))
          .withColumn("adgroup_id", lit(None).cast(StringType()))
          .withColumn("account_name", lit(None).cast(StringType()))
          .withColumn("keyword_id", lit(None).cast(StringType()))
          .withColumn("multiplier_level_end", lit(None).cast(StringType()))
          .withColumn("expected_rank", lit(None).cast(StringType()))
          .withColumn("audience_id", lit(None).cast(StringType())))

    # Add additional columns
    df = df.withColumn(
        'bid_status', when(col('IS_PAUSED') == False, 'ACTIVE').otherwise('PAUSED')
    )
    df = df.withColumn('bid_value', col('BASE_BID').cast(DoubleType()))
    df = df.withColumn('multiplier_value', col('BIDMULTIPLIER_VALUE').cast(DoubleType()))

    # Add constant columns
    df = df.withColumn('channel', lit('META'))
    df = df.withColumn('partner', lit(partner))
    df = df.withColumn(
        'partner_submission_datetime_utc',
        when(lit(finished_at).isNotNull(), to_timestamp(lit(finished_at), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"))
        .otherwise(current_timestamp())
    )

    df = df.withColumn(
        'bsp_submission_datetime_utc',
        when(lit(finished_at).isNotNull(), to_timestamp(lit(finished_at), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"))
        .otherwise(current_timestamp())
    )
    df = df.withColumn('expected_rank', lit('NOT IN USE'))
    df = df.withColumn('partner_submission_id', lit(vulcan_run_id))
    df = df.withColumn('bsp_submission_id', lit(vulcan_run_id))

    # Create the submission_type column based on hotel_id
    df = df.withColumn('submission_type',
                       when(col('hotel_id').isNull(), 'MULTIPLIER').otherwise('BASE_BID'))

    df = df.drop('IS_PAUSED', 'BASE_BID', 'BIDMULTIPLIER_VALUE', 'ENHANCED_CPC', 'CLEARED_BID', 'GROUP_TYPE',
                 'REASON_CODE', 'TRACKING_CODE')

    df.write.orc(outputs_path, compression='ZLIB', mode='append', partitionBy=['channel', 'partner', 'brand', 'partner_submission_id'])
    df = df.withColumn('partner_submission_id', lit('latest'))
    df.write.parquet(parquet_outputs_path, compression='snappy', mode='append', partitionBy=['channel', 'partner', 'brand', 'partner_submission_id'])

for root, dirs, files in os.walk(outputs_path):
    for file in files:
        if file == '_SUCCESS' or file.endswith('.crc'):
            os.remove(os.path.join(root, file))

if os.getenv('ENV', 'DEV') != 'DEV':
    def get_leaf_dir_paths(root_path):
        root = Path(root_path)
        return [
            str(dir_path.resolve())
            for dir_path in root.rglob("*")
            if dir_path.is_dir() and not any(child.is_dir() for child in dir_path.iterdir())
        ]


    full_dir_paths = get_leaf_dir_paths(outputs_path)
    for path in full_dir_paths:
        suffix_path = path.replace(outputs_path, '')
        post_publish_path = f'{os.getenv("POST_PUBLISH_PATH")}/{suffix_path}'
        gmo_s3_path = f's3://{os.getenv("POST_PUBLISH_GMO_BUCKET")}/{post_publish_path}/'
        os.system(f'aws s3 rm --recursive {gmo_s3_path}')
        aws.s3_sync(
            origin=f'{path}/',
            destination=gmo_s3_path,
        )

        logger.info(f"calling airflow to sync {post_publish_path}")
        airflow_conf = {
            'sourceS3Bucket': os.getenv("POST_PUBLISH_GMO_BUCKET"),
            'sourceS3Path': post_publish_path,
            'destinationS3Bucket': os.getenv("POST_PUBLISH_EGDP_BUCKET"),
            'destinationS3Path': post_publish_path,
            'refreshTable': 'marketing.bsp_bid_history'
        }

        airflow_dag_run_id = airflow.start_dag('s3_sync', airflow_conf)
        airflow.monitor_dag_run('s3_sync', airflow_dag_run_id)

    if channel == "google-hotel-ads":
        run_spend_controller(run_labels=labels)

