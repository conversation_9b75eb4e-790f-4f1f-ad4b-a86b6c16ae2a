import argparse
import glob
import json
import os

import requests
from loguru import logger
from pyspark.sql import SparkSession
from pyspark.sql.functions import lit, current_timestamp, to_timestamp

from src.utils import aws, airflow

spark = SparkSession.builder.appName("post_publish").getOrCreate()

def _parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--channel', type=str, help='marketing channel to run for',
    )
    parser.add_argument(
        '--runId', type=str, help='The unique id for the run',
    )
    parser.add_argument(
        '--inputS3Path', type=str, help='The S3 path for the input files',
    )
    parser.add_argument(
        '--labels', type=str, help='The labels for the run',
    )
    parser.add_argument(
        '--parameters', type=str, help='run parameters - json string', required=False,
    )

    return parser.parse_args()

def _get_publish_timestamp(channel, vulcan_run_id):
    ottoman_run = requests.post(
        url=f'{os.getenv("OTTOMAN_API")}/getRuns',
        data=json.dumps(
            {
                'client': 'eg',
                'algorithm': 'mbt',
                'channel': f'MARKETING_CHANNEL_{channel.upper().replace("-", "_")}',
                'labels': [vulcan_run_id],
                'limit': 1
            }),
    )
    try:
        ottoman_run.raise_for_status()
        finished_at = ottoman_run.json()[0].get('finished_at')
        return to_timestamp(lit(finished_at), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS")
    except:
        logger.info(f'Could not resolve runtime from Ottoman service, using post run start time instead')
        return current_timestamp()


def _trigger_history_table_sync(output_path, partner, brand_region_label, vulcan_run_id):
    for root, dirs, files in os.walk(output_path):
        for file in files:
            if file == '_SUCCESS' or file.endswith('.crc'):
                os.remove(os.path.join(root, file))

    post_publish_path = os.getenv('POST_PUBLISH_PATH').format(
        brand=brand_region_label.split('-')[0].upper(),
        channel='META',
        partner=partner,
        runId=vulcan_run_id,
    )
    gmo_s3_path = f's3://{os.getenv("POST_PUBLISH_GMO_BUCKET")}/{post_publish_path}/'

    if os.getenv('ENV', 'DEV') != 'DEV':
        os.system(f'aws s3 rm --recursive {gmo_s3_path}')
        aws.s3_sync(
            origin='/workspace/outputs/',
            destination=gmo_s3_path,
        )

        airflow_conf = {
            'sourceS3Bucket': os.getenv("POST_PUBLISH_GMO_BUCKET"),
            'sourceS3Path': post_publish_path,
            'destinationS3Bucket': os.getenv("POST_PUBLISH_EGDP_BUCKET"),
            'destinationS3Path': post_publish_path,
            'refreshTable': 'marketing.bsp_bid_history'
        }

        airflow_dag_run_id = airflow.start_dag('s3_sync', airflow_conf)
        airflow.monitor_dag_run('s3_sync', airflow_dag_run_id)


def main():
    args = _parse_args()
    channel = args.channel.replace('-', '_')
    run_id = args.runId
    labels = json.loads(args.labels)
    input_s3_path = args.inputS3Path

    # Vulcan's Ottoman call expected to provide these fields in the following order
    brand_region_label = labels[0]
    vulcan_run_id = labels[1]

    logger.info(f'Running post publish for runId: {run_id}, channel: {channel}, labels: {labels}')

    mapping = {}
    partner = "placeholder"
    # Add channel specific table mappings here
    if channel == "trip_advisor":
        import src.trip_advisor.mapping as ta
        logger.info(f'Configured post publish for Trip Advisor')
        mapping = ta.get_bid_history_mapping(brand_region_label, vulcan_run_id, _get_publish_timestamp(channel, vulcan_run_id))
        partner = "TRIPADVISOR"
    if channel == "kayak":
        parameters = json.loads(args.parameters)
        lob = parameters.get("lob", "")
        if lob == 'hotels':
            import src.kayak.mapping as kayak
            logger.info(f'Configured post publish for Kayak')
            mapping = kayak.get_bid_history_mapping(brand_region_label, vulcan_run_id, _get_publish_timestamp(channel, vulcan_run_id))
            partner = "KAYAK"
    if channel == "trivago":
        import src.trivago.mapping as trivago
        logger.info(f'Configured post publish for Trivago')
        mapping = trivago.get_bid_history_mapping(brand_region_label, vulcan_run_id, _get_publish_timestamp(channel, vulcan_run_id))
        partner = "TRIVAGO"
    logger.info(f'Downloading inputs from : {input_s3_path}')
    if os.getenv('ENV', 'DEV') != 'DEV':
        aws.s3_sync(
            origin=input_s3_path,
            destination='/workspace/inputs/'
        )

    input_files = ['/workspace/' + i
                   for i in glob.iglob(f'**/*', root_dir='/workspace/', recursive=True)
                   if i.endswith('.csv.gz')]
    output_path = "/workspace/outputs/"
    os.makedirs(output_path, exist_ok=True)

    for input_file in input_files:
        logger.info(f'Processing {input_file}')
        df = spark.read.csv(input_file, header=True, inferSchema=True)

        for item, val in mapping.items():
            df = df.withColumn(item, val)

        extra_cols = [col for col in df.columns if col not in mapping.keys()]
        df = df.drop(*extra_cols)

        df.write.orc(output_path, compression='ZLIB', mode='append')

    _trigger_history_table_sync(output_path, partner, brand_region_label, vulcan_run_id)

if __name__ == "__main__":
    main()