import argparse
import importlib
import duckdb


parser = argparse.ArgumentParser()
parser.add_argument(
    '--channel', type=str, help='The mcc for the given accountId',
)
parser.add_argument(
    '--runId', type=str, help='The unique id for the run',
)
parser.add_argument(
    '--accountId', type=str, help='The accountId for the run',
)

args = parser.parse_args()
run_id = args.runId
channel = args.channel.replace('-', '_')
algo = importlib.import_module(f'src.{channel}')
con = duckdb.connect('/app/report.db')
con.execute("CREATE OR REPLACE SEQUENCE negative_id_generator START WITH -1 INCREMENT -1")
generator = algo.Generator(args.accountId, run_id, con)
generator.generate_bids()
con.close()