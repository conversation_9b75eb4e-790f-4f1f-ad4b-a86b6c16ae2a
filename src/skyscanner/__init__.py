import glob
import io
import os
from datetime import datetime

import paramiko
import ibis
import pandas as pd
import loguru
from ibis.backends import duckdb as ibis_duck

from src.common.config import Config, SFTPConfig

class SkyscannerWorkflow:
    config: Config
    sftpConfig: SFTPConfig
    duck_con: ibis_duck.Backend
    base_bids_path_final: str
    multipliers_path_final: str

    def __init__(self, config: Config, sftpConfig: SFTPConfig):
        duck_con = ibis.duckdb.connect(config.sys_conf.duckdb.db_file)
        self.duck_con = duck_con
        self.config = config
        self.sftpConfig = sftpConfig

    def generate_bids(self):
        now_str = datetime.now().strftime("%d%m%Y")
        self._load_inputs_bids()
        self._transform_bids()
        self._write_bid_files(now_str)
        self._upload_to_sftp()

    def _load_inputs_bids(self):
        loguru.logger.info('Loading inputs bids')
        input_files = [self.config.sys_conf.bids_local_path + i
                            for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                            if i.endswith(('.csv.gz'))]
        loguru.logger.info(f'Found {len(input_files)} files to be loaded')
        load_stmt = f"CREATE TABLE {self.config.sys_conf.duckdb.bids_working_table} as SELECT * from read_csv({input_files}, union_by_name=true)"
        if self.config.sys_conf.env != 'prod':
            loguru.logger.debug(load_stmt)
        self.duck_con.raw_sql(query=load_stmt)
        loguru.logger.info('Success!')

    def _transform_bids(self):
        loguru.logger.info('Transforming bids')
        bids_table = self.config.sys_conf.duckdb.bids_working_table
        bids_working = self.duck_con.table(bids_table)
        # Filter for BASE_BID and MULTIPLIER rows
        bids_working_base_bids = bids_working.filter(bids_working['submission_type'] == 'BASE_BID')
        bids_working_multipliers = bids_working.filter(bids_working['submission_type'] != 'BASE_BID')

        # Filter for BASE_BID rows
        base_bids_data = bids_working_base_bids.execute()

        # Check for conflicts: group by hotel_id and posa-device, then count occurrences
        base_bids_data['posa_device'] = base_bids_data['posa'] + '-' + base_bids_data['device']
        conflicts = base_bids_data.groupby(['hotel_id', 'posa_device']).size()
        conflicting_rows = conflicts[conflicts > 1]

        # Raise an exception if conflicts are found
        if not conflicting_rows.empty:
            raise Exception(f"Conflicts found in the data: {conflicting_rows.to_dict()}")

        # Create new columns for bid_value and hotel_group
        base_bids_data['column_bid'] = base_bids_data['posa'] + '-' + base_bids_data['device'].str.capitalize()
        base_bids_data['column_group'] = base_bids_data['posa'] + '-' + base_bids_data['device'].str.capitalize() + '-Group'

        # Pivot the data to create the desired structure
        pivot_bid = base_bids_data.pivot(index='hotel_id', columns='column_bid', values='bid_value_usd_cents')
        pivot_group = base_bids_data.pivot(index='hotel_id', columns='column_group', values='hotel_group')

        # Combine the two pivoted DataFrames
        base_bid_result = pd.concat([pivot_bid, pivot_group], axis=1).reset_index()

        # Interleave the columns
        bid_columns = [col for col in base_bid_result.columns if '-' in col and not col.endswith('-Group')]
        group_columns = [col for col in base_bid_result.columns if col.endswith('-Group')]
        ordered_columns = ['hotel_id'] + [col for pair in zip(bid_columns, group_columns) for col in pair]

        # Reorder the DataFrame
        base_bid_result = base_bid_result[ordered_columns]

        # Fill NaN values with empty strings or zeros if needed
        base_bid_result = base_bid_result.fillna(0)
        # Rename the columns
        base_bid_result = base_bid_result.rename(columns={'hotel_id': 'Hotel ID'})
        # Filter for MULTIPLIER rows
        multiplier_data = bids_working_multipliers.execute()

        # Create the new columns
        multiplier_data['Campaign'] = multiplier_data['posa'] + '-' + multiplier_data['device'].str.capitalize()
        # Select and rename the columns
        multipliers_result = multiplier_data[[
            'multiplier_type', 'multiplier_lower', 'multiplier_upper', 'multiplier_value', 'Campaign', 'hotel_group'
        ]].rename(columns={
            'multiplier_lower': 'Multiplier Start',
            'multiplier_upper': 'Multiplier End',
            'multiplier_type': 'Multiplier Type',
            'multiplier_value': 'Multiplier Value',
        })

        # Fill NaN values if needed
        multipliers_result = multipliers_result.fillna({
            'Multiplier Type': '',  # String column
            'Multiplier Start': 0,  # Numeric column
            'Multiplier End': 0,  # Numeric column
            'Multiplier Value': 0,  # Numeric column
            'Campaign': '',  # String column
            'hotel_group': 0  # Numeric column
        })

        drop_stmt = f"DROP TABLE IF EXISTS {self.config.sys_conf.duckdb.bids_final_table}_base_bids"
        self.duck_con.raw_sql(query=drop_stmt)
        drop_stmt = f"DROP TABLE IF EXISTS {self.config.sys_conf.duckdb.bids_final_table}_multipliers"
        self.duck_con.raw_sql(query=drop_stmt)
        self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids", obj=base_bid_result)

        # Create the table
        # Corrected CREATE TABLE statement
        create_stmt = f"""
        CREATE TABLE {self.config.sys_conf.duckdb.bids_final_table}_multipliers (
            "Multiplier Type" TEXT,
            "Multiplier Start" BIGINT,
            "Multiplier End" BIGINT,
            "Multiplier Value" BIGINT,
            "Campaign" TEXT,
            "Group" TEXT
        )
        """
        self.duck_con.raw_sql(query=create_stmt)
        self.duck_con.create_table("multipliers_result", obj=multipliers_result)

        # Insert data into the table
        insert_stmt = f"""
        INSERT INTO {self.config.sys_conf.duckdb.bids_final_table}_multipliers
        SELECT 
            "Multiplier Type",
            "Multiplier Start",
            "Multiplier End",
            "Multiplier Value",
            "Campaign",
            "hotel_group" AS "Group"
        FROM multipliers_result
        """
        self.duck_con.raw_sql(query=insert_stmt)

        loguru.logger.info('Success!')

    def _write_bid_files(self, now_str: str):
        loguru.logger.info('Writing bids to partner file format...')
        os.makedirs(f'{self.config.sys_conf.bids_out}transformed-bids/base-bids/', exist_ok=True)
        os.makedirs(f'{self.config.sys_conf.bids_out}transformed-bids/bid-multipliers/', exist_ok=True)
        base_bids_filename = f'expedia_bidfile_{now_str}.csv'
        multipliers_filename = f'expedia_multiplierfile_{now_str}.csv'

        # base bids
        base_bids_file_path = f'{self.config.sys_conf.bids_out}transformed-bids/base-bids/{base_bids_filename}'
        base_bids_out = f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids"
        self.duck_con.raw_sql(f"COPY (SELECT * FROM {base_bids_out}) TO '{base_bids_file_path}' WITH (HEADER, DELIMITER ',')")

        # multipliers
        multipliers_file_path = f'{self.config.sys_conf.bids_out}transformed-bids/bid-multipliers/{multipliers_filename}'
        multipliers_out = f"{self.config.sys_conf.duckdb.bids_final_table}_multipliers"
        self.duck_con.raw_sql(f"COPY (SELECT * FROM {multipliers_out}) TO '{multipliers_file_path}' WITH (HEADER, DELIMITER ',')")
        self.base_bids_path_final = base_bids_file_path
        self.multipliers_path_final = multipliers_file_path
        loguru.logger.info('Success!')

    def _upload_to_sftp(self):
        loguru.logger.info('Uploading to SFTP')
        ssh_client = paramiko.SSHClient()

        host = self.sftpConfig.host
        username = self.sftpConfig.username
        password = self.sftpConfig.password

        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(host, username=username, password=password)

        loguru.logger.info('connected to sftp')
        sftp = ssh_client.open_sftp()
        try:
            base_bids_filename = self.base_bids_path_final.split('/')[-1]
            multipliers_filename = self.multipliers_path_final.split('/')[-1]
            sftp.put(self.base_bids_path_final, f'/expedia/feed/{base_bids_filename}')
            loguru.logger.info(f"Successfully uploaded {self.base_bids_path_final} to {f'/expedia/feed/{base_bids_filename}'}")
            sftp.put(self.multipliers_path_final, f'/expedia/feed/{multipliers_filename}')
            loguru.logger.info(f"Successfully uploaded {self.multipliers_path_final} to {f'/expedia/feed/{multipliers_filename}'}")
        except Exception as e:
            loguru.logger.error(f"Failed to upload files: {e}")
            raise RuntimeError("SFTP upload failed. Terminating workflow.") from e

        loguru.logger.info('uploaded to sftp successful')
        sftp.close()
        ssh_client.close()
