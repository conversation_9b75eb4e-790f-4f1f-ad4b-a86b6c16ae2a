output:
  columns:
    multipliers:
      hotelGroup: "property_group_id"
      pos: "POS"
      type: "Dimension"
      level: "Breakout"
      value: "Modifier"
      delimiter: ","
    baseBids:
      hotelId: "partner_reference"
      delimiter: ","
    hotelGroups:
      delimiter: ";"
  filename:
    multipliersPrefix: "Wotif_Bid_Modifiers_"
    baseBidsPrefix: "WTF_bids_"
    hotelGroupPrefix: "bids_hotelgroups_"
    datePattern: "ddMMyyyy"
    dateCutoverUtcTime: "03:00" # 3 AM UTC cutoff
  baseBidDefault: 5

input:
  inventory:
    propertyIdColumn: "eg_property_id"