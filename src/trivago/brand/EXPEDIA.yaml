output:
  columns:
    multipliers:
      hotelGroup: "property_group_id"
      pos: "partner_pos"
      type: "dimension"
      level: "breakout"
      value: "modifier"
      delimiter: ","
    baseBids:
      hotelId: "partner_reference"
      delimiter: ","
    hotelGroups:
      delimiter: ";"
  filename:
    multipliersPrefix: "bids_modifiers_"
    baseBidsPrefix: "bids_verb_"
    hotelGroupPrefix: "bids_hotelgroups_"
    datePattern: "yyyy_MM_dd"
    dateCutoverUtcTime: "03:00" # 3 AM UTC cutoff
  baseBidDefault: 5

input:
  inventory:
    propertyIdColumn: "eg_property_id"