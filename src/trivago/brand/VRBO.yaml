output:
  columns:
    multipliers:
      hotelGroup: "property_group_id"
      pos: "partner_pos"
      type: "dimension"
      level: "breakout"
      value: "modifier"
      delimiter: ","
    baseBids:
      hotelId: "partner_reference"
      delimiter: ","
    hotelGroups:
      delimiter: ";"
  filename:
    multipliersPrefix: "Vrbo-BidModifier-3432_"
    baseBidsPrefix: "Vrbo-Bid-3432_"
    hotelGroupPrefix: "Vrbo-BidModifier-Hotelgroups-3432_"
    datePattern: "yyyy_MM_dd"
    dateCutoverUtcTime: "02:00" # 2 AM UTC cutoff
  baseBidDefault: 5

input:
  inventory:
    propertyIdColumn: "eg_property_id"