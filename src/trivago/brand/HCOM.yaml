output:
  columns:
    multipliers:
      hotelGroup: "property_group_id"
      pos: "partner_pos"
      type: "dimension"
      level: "breakout"
      value: "modifier"
      delimiter: ";"
    baseBids:
      hotelId: "eg_property_id"
      delimiter: ";"
    hotelGroups:
      delimiter: ";"
  filename:
    multipliersPrefix: "HCOM_BiddingModifiers_"
    baseBidsPrefix: "HCOM_BaseBid_"
    hotelGroupPrefix: "HCOM_bids_hotelgroups_"
    datePattern: "yyyy-MM-dd'T'HH-mm-ss"
    dateCutoverUtcTime: "00:00" # midnight UTC cutoff
  baseBidDefault: 5

input:
  inventory:
    propertyIdColumn: "eg_property_id"