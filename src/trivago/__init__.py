import glob
import os
import re
from datetime import datetime

import ibis
import loguru
import trino
import yaml
from ibis.backends import duckdb as ibis_duck
from ibis.backends import trino as ibis_trino

from src.common.config import Config
from src.trivago.mapping import BRAND_MAP

def get_brand_config(brand):
    with open(f'src/trivago/brand/{brand.upper()}.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config

class TVGOPartnerWorkflow:
    config: Config
    trino_con: ibis_trino.Backend
    duck_con: ibis_duck.Backend

    def __init__(self, config: Config):
        trino_auth = trino.auth.BasicAuthentication(
            username=config.sys_conf.trino.username,
            password=config.sys_conf.trino.password,
        )
        trino_api = trino.dbapi.connect(
            host=config.sys_conf.trino.host,
            port=config.sys_conf.trino.port,
            catalog=config.sys_conf.trino.catalog,
            http_scheme=config.sys_conf.trino.http_scheme,
            schema=config.sys_conf.trino.schema,
            verify=False,
            auth=trino_auth
        )
        trino_con = ibis_trino.Backend()
        trino_con.con = trino_api
        trino_con.connect()

        duck_con = ibis.duckdb.connect(config.sys_conf.duckdb.db_file)

        self.config = config
        self.trino_con = trino_con
        self.duck_con = duck_con

    # Main entry into job
    def generate_bids(self):
        now_str = datetime.now().strftime("%Y-%m-%dT%H-%M-%S")
        self._load_inputs_bids()
        try:
            self._validate_input_bids()
        except Exception as e:
            loguru.logger.error(f'Validation failed {e}')
        self._validate_input_bids()
        self._transform_bids()
        self._write_bid_files(now_str)


    # Roughly equivalent to the `push_params_to_db` function in the Airflow DAG
    # The bids are downloaded from s3 and stored locally from the file_based_executor.py
    def _load_inputs_bids(self):
        loguru.logger.info('Loading input files into local DB...')
        input_files = [self.config.sys_conf.bids_local_path + i
                       for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                       if i.endswith('.csv.gz')]
        loguru.logger.info(f'Found {len(input_files)} files to be loaded')
        load_stmt = f"CREATE TABLE {self.config.sys_conf.duckdb.bids_working_table} as SELECT * from read_csv({input_files}, union_by_name=true)"
        if self.config.sys_conf.env != 'prod':
            loguru.logger.debug(load_stmt)
        self.duck_con.raw_sql(query=load_stmt)
        loguru.logger.info('Success!')

    def _validate_input_bids(self):
        loguru.logger.info('Processing validation rules on input bids...')
        bids_working = self.duck_con.table(self.config.sys_conf.duckdb.bids_working_table)

        # Submission types
        valid_submissions_types = ["BASE_BID", "MULTIPLIER"]
        invalid_sub_type = (~bids_working.select('submission_type')
                            .distinct()
                            .filter(bids_working['submission_type'].isin(valid_submissions_types))
                            .to_pandas()
                            .any()
        ['submission_type']
                            )
        if invalid_sub_type:
            raise Exception('Bids with submission type not in "BASE_BID", "MULTIPLIER"')

        # POSA value validation
        # All posa + hotel_id keys should be unique when submission_type is "BASE_BID"
        bids_working = bids_working.mutate(entity_key_basebid=bids_working['posa'].concat('.').concat(bids_working['hotel_id'].cast('string')))
        basebid_unique_keys = (bids_working.select('entity_key_basebid', 'submission_type')
                               .filter(bids_working['submission_type'] == 'BASE_BID')
                               .nunique()
                               .to_pandas()
                               )
        basebid_n_keys = (bids_working.select('entity_key_basebid', 'submission_type')
                          .filter(bids_working['submission_type'] == 'BASE_BID')
                          .count()
                          .to_pandas()
                          )
        invalid_unique_key =  basebid_n_keys > basebid_unique_keys
        if invalid_unique_key:
            raise Exception('Base bids with non-unique posa.hotel_id key')

        # All posa + multiplier_type + multiplier_level + hotel_group keys should be unique when submission_type is "MULTIPLIER"
        bids_working = bids_working.mutate(entity_key_multiplier=bids_working['posa']
                                           .concat('.').concat(bids_working['multiplier_type'].cast('string'))
                                           .concat('.').concat(bids_working['multiplier_level'].cast('string'))
                                           .concat('.').concat(bids_working['hotel_group'].cast('string'))
                                           )
        multiplier_unique_keys = (bids_working.select('entity_key_multiplier', 'submission_type')
                                  .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                  .nunique()
                                  .to_pandas()
                                  )
        multiplier_n_keys_count = (bids_working.select('entity_key_multiplier', 'submission_type')
                                   .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                   .count()
                                   .to_pandas()
                                   )
        invalid_unique_key =  multiplier_n_keys_count > multiplier_unique_keys
        if invalid_unique_key:
            raise Exception('Base bids with non-unique posa.hotel_id key')
        # All Posa values should be uppercase
        posa_values = (bids_working.select('posa')
                       .to_pandas()
                       )
        if not posa_values['posa'].str.isupper().all():
            raise Exception('Not all values in posa are uppercase')

        # Metadata validation
        bids_working = bids_working.mutate(
            metadata=bids_working['metadata'].cast('string')
        )
        invalid_metadata = (bids_working.select('metadata')
                            .filter(~bids_working['metadata'].isnull())
                            .filter(~bids_working['metadata'].rlike(r'^\s*\{.*\}\s*$'))
                            .to_pandas()
                            )
        if not invalid_metadata.empty:
            raise Exception('Bids with invalid metadata. Metadata should be a valid JSON string or NULL')


        # Device validation
        # Check for invalid device types for "BASE_BID" submission type
        bids_working = bids_working.mutate(
            device=bids_working['device'].cast('string')
        )
        valid_device_types_basebid = ["DESKTOP", "MOBILE", "TABLET"]

        invalid_device_type_basebid = (bids_working.select('device', "submission_type")
                               .distinct()
                               .filter((bids_working['submission_type'] == 'BASE_BID') &
                                       (~bids_working['device'].isnull() &
                                        ~bids_working['device'].isin(valid_device_types_basebid)
                                        ))
                               .to_pandas()
                               )
        if not invalid_device_type_basebid.empty:
            raise Exception('Bids with submission_type "BASE_BID" and device type not in "DESKTOP", "MOBILE", "TABLET" or NULL')
        # Check for invalid device types for "MULTIPLIER" submission type
        invalid_device_type_multiplier = (bids_working.select('device', "submission_type")
                                          .distinct()
                                          .filter((bids_working['submission_type'] == 'MULTIPLIER') &
                                                  (~bids_working['device'].isnull()))
                                          .to_pandas()
                                          .notnull()
                                          .any()
        ['device'])

        # Raise exception if invalid device type is found for "MULTIPLIER" submission type
        if invalid_device_type_multiplier:
            raise Exception('Bids with submission type "MULTIPLIER" must have device as null')


        # Placement validation
        bids_working = bids_working.mutate(
            placement=bids_working['placement'].cast('string')
        )
        invalid_placement = (bids_working.select('placement', 'submission_type')
                             .filter(bids_working['submission_type'] == 'MULTIPLIER')
                             .filter(~bids_working['placement'].isnull())
                             .to_pandas()
                             )
        if not invalid_placement.empty:
            raise Exception('Bids with submission type "MULTIPLIER" must have placement as null')


        # Hotel_id Validation
        # Step 1: Select the 'hotel_id' column where 'submission_type' is 'BASE_BID'
        hotel_id_values = (bids_working.select('hotel_id', "submission_type")
                           .distinct()
                           .filter(bids_working['submission_type'] == 'BASE_BID').to_pandas()
                           )

        # Step 2: Define the positive integer check
        def is_positive_integer(value):
            return isinstance(value, int) and value >= 0

        # Step 3: Define the UUID regex check
        uuid_regex = re.compile(r'^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$')
        def matches_uuid_regex(value):
            return bool(uuid_regex.match(value))

        # Step 4: Define the empty check
        def is_empty(value):
            return value is None or value == ''

        # Step 5: Combine conditions to filter invalid 'hotel_id' values
        invalid_hotel_id = hotel_id_values[~hotel_id_values['hotel_id'].apply(lambda x: is_positive_integer(x) or matches_uuid_regex(x) or is_empty(x))]

        # Step 6: Raise exception if any invalid 'hotel_id' values are found
        if not invalid_hotel_id.empty:
            raise Exception('Invalid hotel_id values found for BASE_BID submission type. Hotel_id should be a positive integer or a valid UUID')

        # Bid value validation
        bid_value_values = (bids_working.select('bid_value', "submission_type")
                            .filter(bids_working['submission_type'] == 'BASE_BID')
                            .to_pandas()
                            )
        invalid_bid_value_values = bid_value_values[~bid_value_values['bid_value'].apply(lambda x: is_positive_integer(x) or is_empty(x))]
        if not invalid_bid_value_values.empty:
            raise Exception('Invalid bid_value values found for BASE_BID submission type. Bid_value should be a positive integer')

        # Hotel_group validation
        def is_range(value):
            return isinstance(value, int) and 0 <= value <= 49

        hotel_group_values = (bids_working.select('hotel_group')
                            .to_pandas()
                            )
        invalid_hotel_group_values = hotel_group_values[~hotel_group_values['hotel_group'].apply(lambda x: is_range(x))]
        if not invalid_hotel_group_values.empty:
            raise Exception('Invalid hotel_group values found. Hotel_group should be a positive integer between 0 and 49')

        # Multiplier type validation
        valid_multiplier_types = ["LOS", "TTT", "DD", "GS"]
        # Ensure multiplier_type is cast to string
        bids_working = bids_working.mutate(
            multiplier_type=bids_working['multiplier_type'].cast('string')
        )
        invalid_multiplier_types= (~bids_working.select('multiplier_type')
                                     .distinct()
                                     .filter(bids_working['multiplier_type'].isin(valid_multiplier_types))
                                     .to_pandas()
                                     .isin(valid_multiplier_types)
                                     .any()
        ['multiplier_type'])
        if invalid_multiplier_types:
            raise Exception('Bids with invalid multiplier_type. Valid values are "LOS", "TTT", "DD", "GS"')

        # Multiplier level validation
        # switch(
        #     ($multiplier_type/is("DD"), any("0","1")),
        # ($multiplier_type/any("LOS", "TTT"), positiveInteger),
        # ($multiplier_type/is("GS"), any("1","2","3","6")),
        # empty
        # )
        multiplier_level_valid = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter(bids_working['multiplier_type'] == 'DD')
                                  .filter((bids_working['multiplier_level'].cast('float') == 0) | (bids_working['multiplier_level'].cast('float') == 1))
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        multiplier_level_total = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter(bids_working['multiplier_type'] == 'DD')
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        invalid_multiplier_level = multiplier_level_total > multiplier_level_valid
        if invalid_multiplier_level:
            raise Exception('DD multiplier_type bids with multiplier_level not 0 or 1')

        multiplier_level_valid = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter((bids_working['multiplier_type'] == 'LOS') | (bids_working['multiplier_type'] == 'TTT'))
                                  .filter(bids_working['multiplier_level'].cast('float') >= 0)
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        multiplier_level_total = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter((bids_working['multiplier_type'] == 'LOS') | (bids_working['multiplier_type'] == 'TTT'))
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        invalid_multiplier_level = multiplier_level_total > multiplier_level_valid
        if invalid_multiplier_level:
            raise Exception('LOS or TTT multiplier_type bids with multiplier_level not a positive integer')
        valid_multiplier_levels = [1, 2, 3, 6];
        multiplier_level_valid = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter(bids_working['multiplier_type'] == 'GS')
                                  .filter(bids_working['multiplier_level'].cast('float').isin(valid_multiplier_levels))
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        multiplier_level_total = (bids_working.select('multiplier_level', 'multiplier_type')
                                  .filter(bids_working['multiplier_type'] == 'GS')
                                  ['multiplier_level']
                                  .count()
                                  .to_pandas()
                                  )
        invalid_multiplier_level = multiplier_level_total > multiplier_level_valid
        if invalid_multiplier_level:
            raise Exception('GS multiplier_type bids with multiplier_level not 1, 2, 3, or 6')

    def _transform_bids(self):
        bids_table = self.config.sys_conf.duckdb.bids_working_table
        bids_working = self.duck_con.table(bids_table)
        bids_working = bids_working.mutate(
            hotel_id=bids_working['hotel_id'].cast('string'),
            bid_value=bids_working['bid_value'].cast('double'),
            multiplier_value=bids_working['multiplier_value'].cast('double'),
            multiplier_level=bids_working['multiplier_level'].cast('string')
        )
        bids_working_base_bids = bids_working.filter(bids_working['submission_type'] == 'BASE_BID')
        bids_working_multiplier = bids_working.filter(bids_working['submission_type'] == 'MULTIPLIER')
        bids_working_base_bids = bids_working_base_bids.select(
            bids_working_base_bids['hotel_id'],
            bids_working_base_bids['posa'],
            bids_working_base_bids['bid_value'],
            bids_working_base_bids['hotel_group']
        )
        bids_working_base_bids = bids_working_base_bids.mutate(
            account_id=1,
            trivago_entity_brand=ibis.literal(self.config.run_conf.brand)
        )
        bids_working_base_bids = bids_working_base_bids.mutate(
            hotel_id=bids_working_base_bids['hotel_id'].cast('string'),
            posa=bids_working_base_bids['posa'].cast('string'),
            bid_value=bids_working_base_bids['bid_value'].cast('int32'),
            hotel_group=bids_working_base_bids['hotel_group'].cast('string'),
            trivago_entity_brand=bids_working_base_bids['trivago_entity_brand'].cast('string')
        )
        bids_working_base_bids = bids_working_base_bids.rename({
            'trivago_entity.base_bid.partner_reference': 'hotel_id',
            'trivago_entity.base_bid.locale': 'posa',
            'trivago_entity.base_bid.cpc1': 'bid_value',
            'trivago_entity.base_bid.adgroup_id': 'hotel_group',
            'trivago_entity.brand': 'trivago_entity_brand'
        })

        bids_working_multiplier = bids_working_multiplier.select(
            bids_working_multiplier['hotel_group'],
            bids_working_multiplier['posa'],
            bids_working_multiplier['multiplier_type'],
            bids_working_multiplier['multiplier_level'],
            bids_working_multiplier['multiplier_value']
        )

        bids_working_multiplier = bids_working_multiplier.mutate(
            multiplier_value=bids_working_multiplier['multiplier_value'].cast('double').round(2)
        )

        bids_working_multiplier = bids_working_multiplier.mutate(
            account_id=1,
            trivago_entity_brand=ibis.literal(self.config.run_conf.brand)
        )
        bids_working_multiplier = bids_working_multiplier.mutate(
            hotel_group=bids_working_multiplier['hotel_group'].cast('string'),
            posa=bids_working_multiplier['posa'].cast('string'),
            multiplier_type=bids_working_multiplier['multiplier_type'].cast('string'),
            multiplier_level=bids_working_multiplier['multiplier_level'].cast('string'),
            multiplier_value=bids_working_multiplier['multiplier_value'].cast('double'),
            trivago_entity_brand=bids_working_multiplier['trivago_entity_brand'].cast('string')
        )

        bids_working_multiplier = bids_working_multiplier.rename({
            'trivago_entity.multiplier.property_group_id': 'hotel_group',
            'trivago_entity.multiplier.partner_pos': 'posa',
            'trivago_entity.multiplier.dimension': 'multiplier_type',
            'trivago_entity.multiplier.breakout': 'multiplier_level',
            'trivago_entity.multiplier.modifier': 'multiplier_value',
            'trivago_entity.brand': 'trivago_entity_brand',
        })
        drop_stmt = f"DROP TABLE IF EXISTS {self.config.sys_conf.duckdb.bids_final_table}_base_bids"
        self.duck_con.raw_sql(query=drop_stmt)
        drop_stmt = f"DROP TABLE IF EXISTS {self.config.sys_conf.duckdb.bids_final_table}_multipliers"
        self.duck_con.raw_sql(query=drop_stmt)
        self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids", obj=bids_working_base_bids)
        self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_multipliers", obj=bids_working_multiplier)
        loguru.logger.info('Success!')

    def _write_bid_files(self, now_str: str):
        loguru.logger.info('Writing bids to partner file format...')
        os.makedirs(f'{self.config.sys_conf.bids_out}transformed-bids/base-bids/', exist_ok=True)
        os.makedirs(f'{self.config.sys_conf.bids_out}transformed-bids/bid-multipliers/', exist_ok=True)
        # base bids
        base_bids_file_path = f'{self.config.sys_conf.bids_out}transformed-bids/base-bids/{self.config.run_conf.brand.upper()}_base_bids_second_price_auction_{now_str}.csv'
        base_bids_out = f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids"
        self.duck_con.raw_sql(f"COPY (SELECT * FROM {base_bids_out}) TO '{base_bids_file_path}' WITH (HEADER, DELIMITER ',')")

        # multipliers
        multipliers_file_path = f'{self.config.sys_conf.bids_out}transformed-bids/bid-multipliers/{self.config.run_conf.brand.upper()}_modifiers_second_price_auction_{now_str}.csv'
        multipliers_out = f"{self.config.sys_conf.duckdb.bids_final_table}_multipliers"
        # Get the schema of the multipliers_out table
        multipliers_schema = self.duck_con.table(multipliers_out).schema()

        # Print the headers and their types
        for name, dtype in multipliers_schema.items():
            print(f"Header: {name}, Type: {dtype}")
        self.duck_con.raw_sql(f"COPY (SELECT * FROM {multipliers_out}) TO '{multipliers_file_path}' WITH (HEADER, DELIMITER ',')")

        loguru.logger.info('Success!')