
BRAND_MAP = {
    "expedia": "bexg",
    "cheaptickets": "bexg",
    "ebookers": "bexg",
    "lastminute": "bexg",
    "orbitz": "bexg",
    "travelocity": "bexg",
    "wotif": "bexg",
    "hcom": "hcom"
}

def get_bid_history_mapping(brand , vulcan_run_id, timestamp):
    from pyspark.sql.functions import lit, col
    from pyspark.sql.types import StringType, DoubleType

    mapping = {
        "channel": lit('META'),
        "partner": lit("TRIVAGO"),
        "brand": lit(brand.upper()),
        "partner_submission_datetime_utc": timestamp,
        "partner_submission_id": lit(vulcan_run_id),
        "bsp_submission_datetime_utc": timestamp,
        "bsp_submission_id": lit(vulcan_run_id),
        "submission_type": col("submission_type").cast(StringType()),
        "metadata": col("metadata").cast(StringType()),
        "expected_rank": lit('NOT IN USE'),
        "posa": col("posa").cast(StringType()),
        "device": col("device").cast(StringType()),
        "placement": col("placement").cast(StringType()),
        "bid_status": lit(""),
        "experiment_name": col("experiment_name").cast(StringType()),
        "experiment_bucket": col("experiment_bucket").cast(StringType()),
        "bidding_level": lit(""),
        "bid_strategy_type": lit(""),
        "account_id": lit(""),
        "campaign_id": lit(""),
        "campaign_name": lit(""),
        "adgroup_name": lit(""),
        "hotel_id": col("hotel_id").cast(StringType()),
        "hotel_group": col("hotel_group").cast(StringType()),
        "bid_value": col("bid_value").cast(DoubleType()),
        "multiplier_value": col("multiplier_value").cast(DoubleType()),
        "multiplier_type": col("multiplier_type").cast(StringType()),
        "multiplier_level": col("multiplier_level").cast(StringType()),
        "adgroup_id": lit(""),
        "account_name": lit(""),
        "keyword_id": lit(""),
        "multiplier_level_end": col("multiplier_level").cast(StringType()),
        "audience_id": lit(""),
        "portfolio_id": lit(""),
        "portfolio_name": lit(""),
    }
    return mapping
