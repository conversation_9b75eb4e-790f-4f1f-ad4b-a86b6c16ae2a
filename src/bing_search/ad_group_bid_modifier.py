from typing import List
from duckdb import DuckDBPyConnection
from src.bing_search.utils import download_report


class AdGroupBidModifier:
    """
    Manages bid modifiers for ad groups, including loading reports and generating bid files for various modifiers.

    Attributes:
        account_id (str): Unique identifier for the account.
        cur (DuckDBPyConnection): DuckDB connection for executing SQL queries.
        run_id (str): Identifier for the current report run.
        input_file (str): Path to the input CSV file containing bid modifier data.

    Methods:
        generate_bids():
            Orchestrates the bid generation process by loading reports and writing bid data
            for different modifiers (date, device, length of stay, etc.).

        load_ad_group_bid_modifier_report():
            Downloads the ad group bid modifier report and loads it into a DuckDB table
            named 'ad_group_bid_modifier_report'.

        load_ad_group_bid_modifier_input():
            Loads input bid modifier data from a CSV file into a DuckDB table, joining with
            the ad group ID-name mapping.

        write_date_type_bids():
            Writes bids related to date selection types to a CSV file.

        write_device_bids():
            Writes bids related to device types to a CSV file.

        write_day_of_week_bids():
            Writes bids related to day-of-week selections to a CSV file.

        write_length_of_stay_bids():
            Writes bids related to length-of-stay modifiers to a CSV file.

        write_booking_window_bids():
            Writes bids related to booking windows to a CSV file.

        write_check_in_date_bids():
            Writes bids related to check-in dates to a CSV file.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroupBidModifier instance with the specified account ID, run ID, and database connection.

        Args:
            account_id (str): Unique identifier for the account.
            run_id (str): Identifier for the current report run.
            cur (DuckDBPyConnection): DuckDB connection for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}/group.tsv'

    @staticmethod
    def get_report_names() -> List[str]:
        """
        Returns a list of report names related to ad group bid modifiers.

        Returns:
            List[str]: List of report names.
        """
        return [
            'ad-group-device-os-criterion',
            'ad-group-advance-booking-window',
            'ad-group-length-of-stay',
            'ad-group-check-in-day-criterion',
            'ad-group-hotel-date-selection'
        ]

    def generate_bids(self):
        """
        Executes the process of generating ad group bid modifiers by loading reports
        and writing bid data for various modifier types (date, device, length of stay, etc.).
        """
        self.load_ad_group_bid_modifier_input()
        self.write_date_type_bids()
        self.write_booking_window_bids()
        self.write_length_of_stay_bids()
        self.write_day_of_week_bids()
        self.write_device_bids()

    def load_ad_group_bid_modifier_input(self):
        """
        Loads input bid modifier data from a CSV file into a DuckDB table named
        'ad_group_bid_modifier_input', joining with the ad group ID-name mapping.

        Raises:
            Exception: If SQL execution fails during table creation or data insertion.
        """
        ddl = """
                CREATE OR REPLACE TABLE ad_group_bid_modifier_input(
                campaign_id BIGINT,
                campaign_name STRING,
                ad_group_id BIGINT,
                ad_group_name STRING,
                ad_group_type STRING,
                multiplier_type STRING,
                multiplier_level STRING,
                multiplier_value INTEGER
                )
                """
        self.cur.execute(ddl)

        read_csv_query = f"""
                SELECT * FROM read_csv('{self.input_file}', delim='\t', header=true)
                """
        final_query = f"""
                INSERT INTO ad_group_bid_modifier_input
                    (SELECT
                        a.CAMPAIGN_ID,
                        a.CAMPAIGN_NAME,
                        b.ad_group_id,
                        a.GROUP_NAME,
                        a.GROUP_TYPE,
                        a.BIDMULTIPLIER_TYPE,
                        a.BIDMULTIPLIER_LEVEL,
                        a.BIDMULTIPLIER_VALUE
                    FROM ({read_csv_query}) a
                    LEFT JOIN ad_group_id_name_mapping b
                        ON (a.CAMPAIGN_NAME = b.campaign_name AND a.GROUP_NAME = b.ad_group_name)
                    )
                """
        self.cur.execute(final_query)

    def write_date_type_bids(self):
        """
        Writes bids related to date selection types to a CSV file, filtering based on the
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_date_type_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_date_type_bids.csv'
        report = download_report(self.account_id, 'ad-group-hotel-date-selection')
        inner_query = f"""
        SELECT 
            ad_group_id,
            cast(criterion_id as bigint) AS criterion_id,
            CASE 
                WHEN target = 'DefaultSelection' THEN 'default' 
                WHEN target = 'UserSelection' THEN 'selected' 
                ELSE NULL 
            END AS target,
            cast(bid_adjustment as integer) AS bid_modifier
        FROM read_csv('{report}', delim='\t', header=true)
        """
        final_query = f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'bing_ads_entity.ad_group_biddable_criterion.parent_id',
            CAST(a.multiplier_value AS INTEGER) AS 'bing_ads_entity.ad_group_biddable_criterion.bid_adjustment',
            CASE
                WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator')
                ELSE b.criterion_id
            END AS 'bing_ads_entity.ad_group_biddable_criterion.id',
            CASE
                WHEN a.multiplier_level = 'default' THEN 'DEFAULTSELECTION'
                WHEN a.multiplier_level = 'selected' THEN 'USERSELECTION'
                ELSE 'UNSPECIFIED'
            END AS 'bing_ads_entity.ad_group_biddable_criterion.date_selection.type'
            FROM ad_group_bid_modifier_input AS a
            LEFT OUTER JOIN ({inner_query}) b
            ON (a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.target)
            WHERE a.multiplier_type = 'DEFAULT_SEARCH' AND a.multiplier_value != COALESCE(b.bid_modifier, -1000)
            """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)

    def write_device_bids(self):
        """
        Writes bids related to device types to a CSV file, filtering based on the multiplier
        type and comparing with existing bid modifiers.

        The output file is named '{account_id}_device_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_device_bids.csv'
        report = download_report(self.account_id, 'ad-group-device-os-criterion')
        inner_query = f"""
                SELECT 
                cast(criterion_id as bigint) AS criterion_id,
                ad_group_id,
                cast(bid_adjustment as integer) AS bid_modifier,
                CASE 
                    WHEN target = 'Smartphones' THEN 'mobile' 
                    WHEN target = 'Tablets' THEN 'tablet' 
                    WHEN target = 'Computers' THEN 'desktop' 
                    ELSE 'unspecified' 
                END AS target
                FROM read_csv('{report}', delim='\t', header=true)
                """
        self.cur.execute(
            f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'bing_ads_entity.ad_group_device_os_criterion.parent_id',
            CAST(a.multiplier_value AS INTEGER) AS 'bing_ads_entity.ad_group_device_os_criterion.bid_adjustment', 
            CASE 
                WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') 
                ELSE b.criterion_id 
            END AS 'bing_ads_entity.ad_group_device_os_criterion.id',
            CASE
                WHEN a.multiplier_level = 'mobile' THEN 'SMARTPHONES'
                WHEN a.multiplier_level = 'tablet' THEN 'TABLETS'
                WHEN a.multiplier_level = 'desktop' THEN 'COMPUTERS'
                ELSE 'UNSPECIFIED'
            END AS 'bing_ads_entity.ad_group_device_os_criterion.target'
            FROM ad_group_bid_modifier_input a 
            LEFT OUTER JOIN ({inner_query}) b
            ON (a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.target)
            WHERE a.multiplier_type = 'PLATFORM' AND a.multiplier_value != COALESCE(b.bid_modifier, -1000)
            """
        ).df().to_csv(path, index=False, header=True)

    def write_day_of_week_bids(self):
        """
        Writes bids related to day-of-week selections to a CSV file, filtering based on the
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_day_of_week_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_day_of_week_bids.csv'
        report = download_report(self.account_id, 'ad-group-check-in-day-criterion')
        inner_query = f"""
            SELECT
                cast(criterion_id as bigint) AS criterion_id,
                ad_group_id,
                cast(bid_adjustment as integer) AS bid_modifier,
                LOWER(target) AS target
            FROM read_csv('{report}', delim='\t', header=true)
            """
        self.cur.execute(
            f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'bing_ads_entity.ad_group_biddable_criterion.parent_id',
            CAST(a.multiplier_value AS INTEGER) AS 'bing_ads_entity.ad_group_biddable_criterion.bid_adjustment',
            CASE 
                WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') 
                ELSE b.criterion_id 
            END AS 'bing_ads_entity.ad_group_biddable_criterion.id',
            UPPER(a.multiplier_level) AS 'bing_ads_entity.ad_group_biddable_criterion.check_in.day'
            FROM ad_group_bid_modifier_input a 
            LEFT OUTER JOIN ({inner_query}) b
            ON (a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.target)
            WHERE a.multiplier_type = 'CHECK_IN_DAY_OF_WEEK' AND a.multiplier_value != COALESCE(b.bid_modifier, -1000)
            """
        ).df().to_csv(path, index=False, header=True)

    def write_length_of_stay_bids(self):
        """
        Writes bids related to length-of-stay modifiers to a CSV file, filtering based on
        the multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_length_of_stay_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_length_of_stay_bids.csv'
        max_len_days = 30
        max_days_cal_query = f"""
            SELECT
                campaign_id,
                campaign_name,
                ad_group_id,
                ad_group_name,
                multiplier_value AS bid_modifier,
                CAST(multiplier_level AS INTEGER) AS min_nights,
                LAG(CAST(multiplier_level AS INTEGER) - 1, 1, {max_len_days})
                    OVER (PARTITION BY ad_group_name, campaign_name ORDER BY CAST(multiplier_level AS INTEGER) DESC) AS max_nights
            FROM ad_group_bid_modifier_input
            WHERE multiplier_type = 'LENGTH_OF_STAY'
        """

        report = download_report(self.account_id, 'ad-group-length-of-stay')
        report_query = f"""
            SELECT
                cast(criterion_id as bigint) AS criterion_id,
                ad_group_id,
                cast(bid_adjustment as integer) AS bid_modifier,
                min_nights,
                max_nights
            FROM read_csv('{report}', delim='\t', header=true)
        """

        final_query = f"""
                SELECT 
                   {self.account_id} AS account_id,
                   CASE WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') ELSE b.criterion_id END AS 'bing_ads_entity.ad_group_biddable_criterion.id',
                   a.ad_group_id AS 'bing_ads_entity.ad_group_biddable_criterion.parent_id',
                   CAST(a.bid_modifier AS INTEGER) AS 'bing_ads_entity.ad_group_biddable_criterion.bid_adjustment',
                   a.min_nights AS 'bing_ads_entity.ad_group_biddable_criterion.length_of_stay.min_nights',
                   a.max_nights AS 'bing_ads_entity.ad_group_biddable_criterion.length_of_stay.max_nights'
                FROM ({max_days_cal_query}) a 
                LEFT JOIN ({report_query}) b 
                ON (a.ad_group_id= b.ad_group_id AND a.min_nights = b.min_nights AND a.max_nights = b.max_nights)
                WHERE a.bid_modifier != COALESCE(b.bid_modifier, -1000)
        """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)

    def write_booking_window_bids(self):
        """
        Writes bids related to booking windows to a CSV file, filtering based on the
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_booking_window_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_booking_window_bids.csv'
        max_len_days = 330
        max_days_cal_query = f"""
        SELECT campaign_id,
               campaign_name,
               ad_group_id,
               ad_group_name,
               multiplier_value AS bid_modifier,
               CAST(multiplier_level AS INTEGER) AS min_days,
               LAG(CAST(multiplier_level AS INTEGER) - 1, 1, {max_len_days} )
                 OVER (
                   PARTITION BY ad_group_name, campaign_name
                   ORDER BY CAST(multiplier_level AS INTEGER) DESC) AS max_days
        FROM ad_group_bid_modifier_input
        WHERE multiplier_type = 'ADVANCE_BOOKING_WINDOW'
        """

        report = download_report(self.account_id, 'ad-group-advance-booking-window')
        report_query = f"""
                    SELECT 
                          cast(criterion_id as bigint) AS criterion_id,
                          ad_group_id,
                          cast(bid_adjustment as integer) AS bid_modifier,
                          min_days,
                          max_days
                   FROM read_csv('{report}', delim='\t', header=true)
        """

        final_query = f"""
                    SELECT 
                        {self.account_id} AS account_id,
                        CASE WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') ELSE b.criterion_id END AS 'bing_ads_entity.ad_group_biddable_criterion.id',
                        a.ad_group_id AS 'bing_ads_entity.ad_group_biddable_criterion.parent_id',
                        CAST(a.bid_modifier AS INTEGER) AS 'bing_ads_entity.ad_group_biddable_criterion.bid_adjustment',
                        a.min_days AS 'bing_ads_entity.ad_group_biddable_criterion.advance_booking_window.min_days',
                        a.max_days AS 'bing_ads_entity.ad_group_biddable_criterion.advance_booking_window.max_days'
                    FROM ({max_days_cal_query}) a 
                    LEFT JOIN ({report_query}) b 
                    ON (a.ad_group_id= b.ad_group_id AND a.min_days = b.min_days AND a.max_days = b.max_days)
                    WHERE a.bid_modifier != COALESCE(b.bid_modifier, -1000)
                """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)
