import json

from duckdb import DuckDBPyConnection

from src.bing_search.utils import download_report


class AdGroup:
    """
    Manages ad group data and creates mappings for ad group IDs and names for a specified account.

    Attributes:
        account_id (str): Unique identifier for the account.
        cur (DuckDBPyConnection): DuckDB connection for executing SQL queries.
        run_id (str): Identifier for the current report run.
        input_file (str): Path to the input CSV file containing ad group data.

    Methods:
        generate_bids():
            Orchestrates the process of generating ad group bids by loading the ad group report
            and mapping ad group IDs to names.

        load_ad_group_report():
            Downloads the ad group report and stores it in a DuckDB table named 'ad_group_report'.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroup instance with the specified account ID, run ID, and database connection.

        Args:
            account_id (str): Unique identifier for the account.
            run_id (str): Identifier for the current report run.
            cur (DuckDBPyConnection): DuckDB connection for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}.tsv'

    def generate_bids(self):
        """
        Executes the bid generation process, which includes loading the ad group report
        and creating the ID-name mapping.
        """
        self.load_ad_group_report()
        self.generate_ad_group_id_name_mapping()
        self.generate_new_ad_groups()

    def load_ad_group_report(self):
        """
        Downloads the ad group report using the account ID and run ID, then loads it into a DuckDB table
        named 'ad_group_report'. The table includes campaign IDs, campaign names, ad group IDs, and ad group names.

        Raises:
            Exception: If report download or SQL execution fails.
        """
        file = download_report(self.account_id, AdGroup.get_report_name())
        read_query = f"""SELECT campaign_id, campaign_name, ad_group_id, ad_group_name FROM read_csv('{file}', delim='\t', header=true)"""
        final_query = f"""INSERT INTO ad_group_report({read_query})"""
        self.cur.execute("""
                CREATE OR REPLACE TABLE ad_group_report(
                campaign_id BIGINT,
                campaign_name STRING,
                ad_group_id BIGINT,
                ad_group_name STRING
                )
                """)
        self.cur.execute(final_query)

    def generate_ad_group_id_name_mapping(self):
        """
        Creates a mapping of ad group IDs to their corresponding names and types, based on the input CSV file.
        The mapping is stored in the 'ad_group_id_name_mapping' table.

        Raises:
            Exception: If the SQL execution fails during the creation of the mapping table or when inserting data.
        """
        ddl = """
                CREATE OR REPLACE TABLE ad_group_id_name_mapping (
                    campaign_id BIGINT,
                    campaign_name string,
                    ad_group_id BIGINT,
                    ad_group_name string,
                    ad_group_type string
                )
                """
        self.cur.execute(ddl)

        read_csv_query = f"""
                    SELECT 
                        DISTINCT 
                        CAMPAIGN_ID as campaign_id,
                        CAMPAIGN_NAME AS campaign_name, 
                        GROUP_NAME AS ad_group_name, 
                        GROUP_TYPE AS ad_group_type
                    FROM read_csv('{self.input_file}', delim='\t', header=true)
                    WHERE BIDDING_LEVEL IN ('GROUP', 'HOTEL')
                """

        final_query = f"""
                INSERT INTO ad_group_id_name_mapping
                    (SELECT
                        a.campaign_id AS campaign_id,
                        a.campaign_name AS campaign_name,
                        CASE 
                            WHEN b.ad_group_id > 0 THEN b.ad_group_id 
                            ELSE nextval('negative_id_generator') 
                        END AS ad_group_id,
                        a.ad_group_name AS ad_group_name,
                        a.ad_group_type AS ad_group_type
                    FROM ({read_csv_query}) a 
                    LEFT JOIN ad_group_report b
                        ON (a.campaign_name = b.campaign_name AND a.ad_group_name = b.ad_group_name)
                    )
                """
        self.cur.execute(final_query)

    def generate_new_ad_groups(self):
        """
        Generates a list of new ad groups from the ad group ID name mapping table.

        New ad groups are identified by negative ad group IDs. The method selects relevant
        fields, including campaign ID, ad group ID, ad group name, ad group type, and status,
        which is set to 'ENABLED'.

        The resulting data is written to a CSV file named '{account_id}_new_ad_groups.csv'
        located in the '/workspace/bids/' directory.

        Raises:
            Exception: If SQL execution fails or if there is an error during file writing.
        """
        path = f'/workspace/bids/{self.account_id}_new_ad_groups.csv'
        query = f"""
            SELECT
                {self.account_id} as account_id,
                campaign_id AS 'bing_ads_entity.ad_group.parent_id',
                ad_group_id AS 'bing_ads_entity.ad_group.id',
                ad_group_name AS 'bing_ads_entity.ad_group.ad_group',
                'ACTIVE' AS 'bing_ads_entity.ad_group.status',
                ad_group_type AS 'bing_ads_entity.ad_group.hotel_setting.hotel_ad_group_types',
                0.05 AS 'bing_ads_entity.ad_group.cpc_bid'
            FROM ad_group_id_name_mapping
            WHERE ad_group_id < 0
        """
        df = self.cur.execute(query).df()
        df ['bing_ads_entity.ad_group.hotel_setting.hotel_ad_group_types'] = df['bing_ads_entity.ad_group.hotel_setting.hotel_ad_group_types'].apply(lambda x: json.dumps(['HOTEL_AD'])if x == 'HotelAd' else json.dumps(['PROPERTY_AD']))
        if not df.empty:
            df.to_csv(path, index=False, header=True)

    @staticmethod
    def get_report_name() -> str:
        """
        Returns the name of the ad group report.

        Returns:
            str: The name of the report ('ad-group').
        """
        return 'ad-group'
