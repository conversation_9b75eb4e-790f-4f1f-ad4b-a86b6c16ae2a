import json
import os

from duckdb import DuckDBPyConnection

from src.bing_search.ad_group import AdGroup
from src.bing_search.ad_group_bid_modifier import AdGroupBidModifier
from src.bing_search.ad_group_criterion import AdGroupCriterion
from src.bing_search.campaign_criterion import CampaignCriterion


class Generator:
    run_id: str
    account: str
    con: DuckDBPyConnection

    def __init__(self, account_id, run_id, con):
        self.account = account_id
        self.run_id = run_id
        self.con = con

    def generate_bids(self):
        os.makedirs('/workspace/bids', exist_ok=True)
        campaign_criterion_generator = CampaignCriterion(self.account, self.run_id, self.con)
        campaign_criterion_generator.generate_bids()
        ad_group_generator = AdGroup(self.account, self.run_id, self.con)
        ad_group_generator.generate_bids()
        ad_group_bid_modifier_generator = AdGroupBidModifier(self.account, self.run_id, self.con)
        ad_group_bid_modifier_generator.generate_bids()
        ad_group_criterion = AdGroupCriterion(self.account, self.run_id, self.con)
        ad_group_criterion.generate_bids()
        self.con.close()

    @staticmethod
    def generate_reports_to_download(run_id):
        reports = [CampaignCriterion.get_report_name(), AdGroup.get_report_name(),  AdGroupCriterion.get_report_name()]
        reports.extend(AdGroupBidModifier.get_report_names())

        with open('/workspace/tmp/reports.json', 'w') as f:
            json.dump(reports, f)
