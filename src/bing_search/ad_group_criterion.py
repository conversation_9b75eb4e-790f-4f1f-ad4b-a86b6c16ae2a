import pandas as pd
from duckdb import DuckDBPyConnection
from src.bing_search.utils import download_report


class AdGroupCriterion:
    """
    Manages ad group criteria for hotel reports, including loading reports, mapping parent criteria,
    and writing hotel bids based on input data.

    Attributes:
        account_id (str): Unique identifier for the account.
        cur (DuckDBPyConnection): DuckDB connection object for executing SQL queries.
        run_id (str): Unique identifier for the current run of the report.
        input_file (str): Path to the input hotel bids data CSV file.

    Methods:
        generate_bids(): Orchestrates the bid generation process.
        load_hotel_report(): Downloads and loads the hotel report.
        generate_ad_group_parent_criterion_mapping(): Creates a mapping of ad group parent criteria.
        load_input_bids(): Loads input bid data from a CSV file.
        write_hotel_bids(): Writes hotel bid data to CSV files based on paused status.
        detect_moved_hotels(): Detects and writes moved hotels to a CSV file.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        self.account_id = account_id
        self.run_id = run_id
        self.cur = cur
        self.input_file = f'/workspace/inputs/{account_id}/hotel.tsv'

    @staticmethod
    def get_report_name() -> str:
        return 'ad-group-hotel-listing-group'

    def generate_bids(self):
        """Executes the full process of generating hotel bids."""
        self.load_hotel_report()
        self.generate_ad_group_parent_criterion_mapping()
        self.load_input_bids()
        self.write_hotel_bids()

    def load_hotel_report(self):
        """Downloads the hotel report and loads it into a DuckDB table."""
        file = download_report(self.account_id, AdGroupCriterion.get_report_name())
        self.cur.execute("""CREATE OR REPLACE TABLE hotel_report(
                    criterion_id BIGINT,
                    ad_group_id BIGINT,
                    campaign_name varchar,
                    base_bid float,
                    hotel_id BIGINT,
                    hotel_type varchar,
                    parent_ad_group_criterion BIGINT
                    )""")
        read_query = f"""
            SELECT
                criterion_id,
                ad_group_id,
                campaign_name,
                base_bid,
                listing_attribute,
                listing_type,
                listing_parent_criterion_id
            FROM read_csv('{file}', delim='\t', header=true)
            WHERE listing_operand IN ('All', 'HotelID')
        """
        self.cur.execute(f'INSERT INTO hotel_report ({read_query})')

    def generate_ad_group_parent_criterion_mapping(self):
        """Creates a mapping of ad group parent criteria."""
        self.cur.execute("""
        CREATE OR REPLACE TABLE ad_group_parent_criterion_mapping(
            campaign_id BIGINT,
            campaign_name string,
            ad_group_id BIGINT,
            ad_group_name string,
            ad_group_type string,
            parent_criterion_id BIGINT
        )
        """)
        final_query = f"""
        INSERT INTO ad_group_parent_criterion_mapping (
            SELECT 
                a.*, 
                COALESCE(b.criterion_id, nextval('negative_id_generator')) AS parent_criterion_id
            FROM ad_group_id_name_mapping a 
            LEFT JOIN (
                SELECT criterion_id, ad_group_id 
                FROM hotel_report 
                WHERE hotel_type = 'Subdivision' 
            ) b
            ON (a.ad_group_id = b.ad_group_id)
        )
        """
        self.cur.execute(final_query)

    def load_input_bids(self):
        """Loads input bid data from a CSV file."""
        self.cur.execute(""" CREATE OR REPLACE TABLE hotel_input(
            hotel_id BIGINT,
            campaign_id BIGINT,
            campaign_name string,
            ad_group_id BIGINT,
            ad_group_name string,
            base_bid float,
            is_paused string,
            parent_criterion_id BIGINT
        )""")
        read_csv_query = f"SELECT * FROM read_csv('{self.input_file}', delim='\t', header=true)"
        final_query = f"""
        INSERT INTO hotel_input 
        SELECT
            a.HOTEL_ID, 
            b.CAMPAIGN_ID, 
            a.CAMPAIGN_NAME, 
            b.ad_group_id, 
            a.GROUP_NAME, 
            a.BASE_BID,
            a.IS_PAUSED,
            b.parent_criterion_id 
        FROM ({read_csv_query}) a 
        LEFT JOIN ad_group_parent_criterion_mapping b 
        ON (a.CAMPAIGN_NAME = b.campaign_name AND a.GROUP_NAME = b.ad_group_name)
        """
        self.cur.execute(final_query)

    def write_hotel_bids(self):
        """Writes hotel bid data to CSV files based on paused status."""
        hotel_bids_path = f'/workspace/bids/{self.account_id}_hotel_bids.csv'
        deleted_hotel_bids_path = f'/workspace/bids/{self.account_id}_deleted_hotel_bids.csv'
        hotel_bids_header, deleted_hotel_bids_header = self.generate_parent_ad_group_criterion_bids(hotel_bids_path, deleted_hotel_bids_path)

        un_paused_query = f"""
        SELECT
            {self.account_id} AS account_id,
            COALESCE(CAST(b.criterion_id AS BIGINT), nextval('negative_id_generator')) AS 'bing_ads_entity.ad_group_criterion.id',
            a.ad_group_id AS 'bing_ads_entity.ad_group_criterion.parent_id',
            'UNIT' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.type',
            'ACTIVE' AS 'bing_ads_entity.ad_group_criterion.status',
            'False' AS 'bing_ads_entity.ad_group_criterion.is_negative',            
            a.parent_criterion_id AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.parent_criterion_id',
            'HotelID' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.operand',
            a.hotel_id AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.attribute',
            a.base_bid AS 'bing_ads_entity.ad_group_criterion.fixed_bid'
        FROM hotel_input a 
        LEFT JOIN hotel_report b 
        ON (a.hotel_id = b.hotel_id AND a.ad_group_id = b.ad_group_id AND a.campaign_name = b.campaign_name) 
        WHERE a.is_paused = 'false' AND a.base_bid != COALESCE(b.base_bid, 0.0)
        ORDER BY a.ad_group_id
        """
        self.cur.execute(un_paused_query).df().to_csv(hotel_bids_path, index=False, header=hotel_bids_header, mode='a')

        paused_query = f"""
        SELECT
            {self.account_id} AS account_id,
            CAST(b.criterion_id AS BIGINT) AS 'bing_ads_entity.ad_group_criterion.id',
            b.ad_group_id AS 'bing_ads_entity.ad_group_criterion.parent_id',
            'DELETED' AS 'bing_ads_entity.ad_group_criterion.status'
        FROM hotel_input a
        INNER JOIN hotel_report b
        ON (a.hotel_id = b.hotel_id AND a.ad_group_id = b.ad_group_id AND a.campaign_name = b.campaign_name)
        WHERE a.is_paused != 'false'
        ORDER BY b.ad_group_id
        """
        paused_bids_df = self.cur.execute(paused_query).df()
        if not paused_bids_df.empty:
            paused_bids_df.to_csv(deleted_hotel_bids_path, index=False, header=deleted_hotel_bids_header, mode='a')
            deleted_hotel_bids_header = False
        self.detect_moved_hotels(deleted_hotel_bids_path, deleted_hotel_bids_header)

    def detect_moved_hotels(self, deleted_hotel_bids_path, deleted_hotel_bids_header):
        """Detects moved hotels and writes to a CSV file with status set to DELETED."""

        query = f"""
        SELECT
            {self.account_id} AS account_id,
            b.criterion_id AS 'bing_ads_entity.ad_group_criterion.id',
            b.ad_group_id AS 'bing_ads_entity.ad_group_criterion.parent_id',
            'DELETED' AS 'bing_ads_entity.ad_group_criterion.status'
        FROM hotel_input a 
        INNER JOIN hotel_report b 
        ON (a.hotel_id = b.hotel_id AND a.campaign_name = b.campaign_name AND a.ad_group_id != b.ad_group_id)
        ORDER BY b.ad_group_id
        """
        df = self.cur.execute(query).df()
        if not df.empty:
            df.to_csv(deleted_hotel_bids_path, index=False, header=deleted_hotel_bids_header, mode='a')

    def generate_parent_ad_group_criterion_bids(self, hotel_bids_path, deleted_hotels_path, hotel_bids_header=True, deleted_bids_header=True):
        """Generates parent ad group criterion bids and saves them to a CSV file."""

        delete_criterions_query = f"""
        SELECT
            {self.account_id} AS account_id,
            criterion_id AS 'bing_ads_entity.ad_group_criterion.id',
            ad_group_id AS 'bing_ads_entity.ad_group_criterion.parent_id',
            'DELETED' AS 'bing_ads_entity.ad_group_criterion.status'
        from hotel_report
        where hotel_type = 'Unit' and parent_ad_group_criterion is null and ad_group_id in (SELECT DISTINCT ad_group_id FROM hotel_input) 
        """
        delete_criterion_df = self.cur.execute(delete_criterions_query).df()
        if not delete_criterion_df.empty:
            delete_criterion_df.to_csv(deleted_hotels_path, index=False, header=deleted_bids_header, mode='a')
            deleted_bids_header = False

        query = f"""
        SELECT 
            {self.account_id} AS account_id,
            parent_criterion_id AS 'bing_ads_entity.ad_group_criterion.id',
            ad_group_id AS 'bing_ads_entity.ad_group_criterion.parent_id',
            'SUBDIVISION' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.type',
            'ACTIVE' AS 'bing_ads_entity.ad_group_criterion.status',
            'False' AS 'bing_ads_entity.ad_group_criterion.is_negative',
            '' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.parent_criterion_id',
            'All' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.operand',
            '' AS 'bing_ads_entity.ad_group_criterion.hotel_listing_group.attribute',
            '' AS 'bing_ads_entity.ad_group_criterion.fixed_bid'
        FROM ad_group_parent_criterion_mapping
        WHERE parent_criterion_id < 0
        """

        sub_division_df = self.cur.execute(query).df()
        temp_id = int(self.cur.execute("SELECT nextval('negative_id_generator')").fetchone()[0])

        # Create negative rows
        others_node_rows = []
        for _, row in sub_division_df.iterrows():
            new_row = row.copy()
            new_row['bing_ads_entity.ad_group_criterion.hotel_listing_group.operand'] = 'HotelID'
            new_row['bing_ads_entity.ad_group_criterion.is_negative'] = 'True'
            new_row['bing_ads_entity.ad_group_criterion.hotel_listing_group.type'] = 'UNIT'
            new_row['bing_ads_entity.ad_group_criterion.hotel_listing_group.parent_criterion_id'] = row['bing_ads_entity.ad_group_criterion.id']
            new_row['bing_ads_entity.ad_group_criterion.id'] = temp_id
            others_node_rows.append(new_row)
            temp_id -= 1

        others_node_df = pd.DataFrame(others_node_rows, columns=sub_division_df.columns)

        # Combine both DataFrames
        combined_rows = []
        for index in range(len(sub_division_df)):
            combined_rows.append(sub_division_df.iloc[index])
            combined_rows.append(others_node_df.iloc[index])

        final_df = pd.DataFrame(combined_rows).reset_index(drop=True)
        if not final_df.empty:
            final_df.to_csv(hotel_bids_path, index=False, header=hotel_bids_header, mode='a')
            hotel_bids_header = False
        return hotel_bids_header, deleted_bids_header