import os

from src.splitter import config
from src.utils import aws


def download_report(account, resource) -> str:
    destination = f'{config.SPLITTER_OUTPUT_PATH}/{account}/{resource}_report.tsv'
    s3_path = f'{os.getenv("BING_REPORT_S3_PATH")}/eg/{resource}/customer_id={account}/dt=latest/file.tsv'
    aws.s3_cp(
        origin=s3_path,
        destination=destination,
    )
    return destination
