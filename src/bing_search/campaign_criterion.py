from duckdb import DuckDBPyConnection
from src.bing_search.utils import download_report


class CampaignCriterion:
    """
    A class to manage campaign criteria and generate user country bids for a specified account.

    Attributes:
        account_id (str): The unique identifier for the account.
        cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        run_id (str): The unique identifier for the current run of the report.
        input_file (str): The file path to the input campaign criteria CSV file.

    Methods:
        generate_bids():
            Orchestrates the process of generating user country bids by loading necessary data.

        load_geo_report():
            Downloads the geographical report and loads it into a DuckDB table.

        load_input_campaign_criterion():
            Loads campaign criteria from a specified CSV file into a DuckDB table.

        write_user_country_bids():
            Writes the generated user country bids to a CSV file.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the CampaignCriterion instance with the given account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}/campaign.tsv'

    @staticmethod
    def get_report_name() -> str:
        return 'campaign-target-criterion'

    def generate_bids(self):
        """
        Executes the full process of generating user country bids, including loading the geo report,
        loading the input campaign criteria, and writing the user country bids to a CSV file.
        """
        self.load_geo_report()
        self.load_input_campaign_criterion()
        self.load_campaign_criterion_report()
        self.write_user_country_bids()

    def load_campaign_criterion_report(self):
        """
        Loads the campaign criterion report into a DuckDB table.
        """
        file = download_report(self.account_id, CampaignCriterion.get_report_name())
        self.cur.execute("""
            CREATE OR REPLACE TABLE campaign_criterion_report (
                campaign_id BIGINT,
                criterion_id BIGINT,
                location_id BIGINT,
                bid_multiplier INTEGER
            )
        """)
        read_query = f"""
            INSERT INTO campaign_criterion_report
            SELECT campaign_id, criterion_id, target, bid_adjustment 
            FROM read_csv('{file}', delim='\t', header=true) 
            WHERE criterion_type = 'Campaign Location Criterion'
        """
        self.cur.execute(read_query)

    def load_geo_report(self):
        """
        Loads the geographical report into a DuckDB table named 'geo_report'.
        """
        geo_targets_file = '/app/resources/geo_targets.csv'
        self.cur.execute("CREATE OR REPLACE TABLE geo_report(country STRING, id INTEGER)")
        read_query = f"""
            INSERT INTO geo_report
            SELECT Bing_Display_Name, Location_Id 
            FROM read_csv('{geo_targets_file}', delim=',', header=true) 
            WHERE Location_Type = 'Country'
        """
        self.cur.execute(read_query)

    def load_input_campaign_criterion(self):
        """
        Loads campaign criteria from a CSV file located at the input file path into a DuckDB table
        named 'campaign_criterion_input'.
        """
        self.cur.execute("""
            CREATE OR REPLACE TABLE campaign_criterion_input (
                campaign_id BIGINT,
                campaign_name STRING,
                multiplier_type STRING,
                multiplier_level STRING,
                multiplier_value INTEGER
            )
        """)
        self.cur.execute(f"COPY campaign_criterion_input FROM '{self.input_file}' (HEADER TRUE, DELIMITER '\t')")

    def write_user_country_bids(self):
        """
        Generates user country bids based on the loaded campaign criteria and geographical data,
        and writes the results to a CSV file.
        """
        path = f'/workspace/bids/{self.account_id}_user_country_bids.csv'
        final_query = f"""
            SELECT
                {self.account_id} AS 'account_id',
                a.campaign_id AS 'bing_ads_entity.campaign_location_criterion.campaign_id',
                a.bid_modifier AS 'bing_ads_entity.campaign_location_criterion.bid_adjustment',
                a.location_id AS 'bing_ads_entity.campaign_location_criterion.location_id',
                COALESCE(b.criterion_id, nextval('negative_id_generator')) AS 'bing_ads_entity.campaign_location_criterion.id'
            FROM (
                SELECT 
                    a.campaign_id, 
                    a.multiplier_value AS bid_modifier,
                    b.id AS location_id,
                FROM campaign_criterion_input a 
                LEFT JOIN geo_report b 
                ON a.multiplier_level = b.country 
                WHERE a.multiplier_type = 'PARTNER_POS'
            ) a 
            LEFT JOIN campaign_criterion_report b ON (a.campaign_id = b.campaign_id and a.location_id = b.location_id)
            WHERE a.bid_modifier != COALESCE(b.bid_multiplier, -100)
        """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True, sep=",")
