import os

import loguru
import paramiko

from common.config import Config, SFTPConfig


class CheapFlightsWorkflow:
    sftpConfig: SFTPConfig
    config: Config
    base_bid_file_path: str

    sftp_root_folder: str = os.environ.get('SFTP_LOCATION', '/bsp_test')

    def __init__(self, config: Config, sftpConfig: SFTPConfig):
        self.sftpConfig = sftpConfig
        self.config = config
        for filename in os.listdir(self.config.sys_conf.bids_local_path):
            if filename.endswith('.xlsx'):
                self.base_bid_file_path  = os.path.join(self.config.sys_conf.bids_local_path, filename)
                break

    def generate_bids(self):
        try:
            self._upload_to_sftp()
        except Exception as e:
            loguru.logger.error(f'Error in generate_bids: {e}')


    def _upload_to_sftp(self):
        loguru.logger.info('Uploading to SFTP')
        ssh_client = paramiko.SSHClient()

        host = self.sftpConfig.host
        username = self.sftpConfig.username
        password = self.sftpConfig.password

        with open('./cheapflights.pub', 'w') as f:
            f.write(password)
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(host, username=username, key_filename='./cheapflights.pub')

        loguru.logger.info('connected to sftp')
        sftp = ssh_client.open_sftp()
        try:
            bids_file_name = self.base_bid_file_path.split('/')[-1]
            server_file = os.path.join(self.sftp_root_folder, bids_file_name)
            sftp.put(self.base_bid_file_path, server_file)
            loguru.logger.info(f"Successfully uploaded {self.base_bid_file_path} to {server_file}")
        except Exception as e:
            loguru.logger.error(f"Failed to upload files: {e}")
            raise RuntimeError("SFTP upload failed. Terminating workflow.") from e

        loguru.logger.info('uploaded to sftp successful')
        sftp.close()
        ssh_client.close()
