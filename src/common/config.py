import dataclasses
import os
import yaml
from typing import Optional


import loguru


@dataclasses.dataclass
class DuckDBConfig:
    db_file: str
    bids_working_table: str
    bids_final_table: str
    vulcan_ids: str
    inventory_working: str
    inventory_final: str


@dataclasses.dataclass
class TrinoConfig:
    host: str
    port: int
    catalog: str
    http_scheme: str
    schema: str
    username: str
    password: str


@dataclasses.dataclass
class RunConfig:
    run_id: str
    input_s3_path: str
    channel: str
    brand: str


@dataclasses.dataclass
class SystemConfig:
    env: str
    bids_local_path: str
    bids_out: str
    bin_file: str
    egumi_table: str
    vulcan_update_table: str
    trino: TrinoConfig
    duckdb: DuckDBConfig


@dataclasses.dataclass
class Config:
    run_conf: RunConfig
    sys_conf: SystemConfig


@dataclasses.dataclass
class SFTPConfig:
    password: str
    username: Optional[str] = None
    host: Optional[str] = None

def setup_config(run_id, channel, input_s3_path, brand) -> Config:
    run_conf = RunConfig(run_id=run_id, channel=channel, input_s3_path=input_s3_path, brand=brand)

    # Parse setup file and environment for system config
    env = 'prod' if os.environ.get("ENV") == 'PROD' else 'dev'
    loguru.logger.info(f'Running as {env} environment')
    setup_data = _parse_setup_yaml(env)
    bids_local_path = setup_data['paths']['bids_local']
    bids_out = setup_data['paths']['bids_out']
    bin_file = setup_data['paths']['binary_out']
    egumi_table = setup_data['integrations']['egumi']
    vulcan_update_table = setup_data['integrations']['vulcan_update']
    duckdb_data = DuckDBConfig(**setup_data['duckdb'])

    # Trino creds
    if env == 'prod':
        trino_user = os.environ.get('TRINO_USER')
        trino_pass = os.environ.get('TRINO_PASS')
    else:
        try:
            with open('./src/secrets.yaml', 'r') as f:
                data = yaml.safe_load(f)
                trino_user = data.get('trino_user')
                trino_pass = data.get('trino_pass')
        except Exception as e:
            loguru.logger.warning('Could not parse secrets from yaml')
            trino_user = 'user'
            trino_pass = 'pass'

    trino_data = TrinoConfig(**setup_data['trino'],
                             username=trino_user,
                             password=trino_pass)

    sys_conf = SystemConfig(env=env,
                            bids_local_path=bids_local_path,
                            bids_out=f"{bids_out}{brand.upper()}/",
                            bin_file=bin_file,
                            egumi_table=egumi_table,
                            vulcan_update_table=vulcan_update_table,
                            trino=trino_data,
                            duckdb=duckdb_data)
    return Config(run_conf=run_conf, sys_conf=sys_conf)


def _parse_setup_yaml(env: str) -> {}:
    with open('./src/setup.yaml', 'r') as f:
        config = yaml.safe_load(f)
    return config.get(env)

def setup_sftp() -> SFTPConfig:
    config_username = None
    config_password = None
    config_host = None

    if os.environ.get('SFTP_HOST'):
        config_host = os.environ.get('SFTP_HOST')
    if os.environ.get('SFTP_USERNAME'):
        config_username = os.environ.get('SFTP_USERNAME')
    if os.environ.get('SFTP_PASS'):
        config_password = os.environ.get('SFTP_PASS')
    return SFTPConfig(
        password=config_password,
        username=config_username,
        host=config_host
    )
