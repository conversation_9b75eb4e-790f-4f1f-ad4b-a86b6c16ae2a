import os

from duckdb import DuckDBPyConnection

from src.google_hotel_ads.utils import download_report


class AdGroup:
    """
    A class to manage ad group data and generate mappings for ad group IDs and names for a specified account.

    Attributes:
        account_id (str): The unique identifier for the account.
        cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        run_id (str): The unique identifier for the current run of the report.
        input_file (str): The file path to the input ad group data CSV file.

    Methods:
        generate_bids():
            Orchestrates the process of generating ad group bids by loading the ad group report
            and generating the ad group ID-name mapping.

        load_ad_group_report():
            Downloads the ad group report and loads it into a DuckDB table named 'ad_group_report'.

        generate_ad_group_id_name_mapping():
            Generates a mapping of ad group IDs to their corresponding names and types,
            and stores this mapping in the 'ad_group_id_name_mapping' table.

        generate_new_ad_groups():
            Generates new ad group IDs and names for a specified account.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroup instance with the given account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}.tsv'

    def generate_bids(self):
        """
        Executes the full process of generating ad group bids, which includes loading the ad group report
        and generating the ad group ID-name mapping.
        """
        self.load_ad_group_report()
        self.generate_ad_group_id_name_mapping()
        self.generate_new_ad_groups()

    def load_ad_group_report(self):
        """
        Downloads the ad group report using the run ID and account ID, and loads it into a DuckDB table
        named 'ad_group_report'. The table includes campaign IDs, campaign names, ad group IDs, and ad group names.

        Raises:
            Exception: If the report download fails or SQL execution fails.
        """
        file = download_report(self.run_id, self.account_id, 'ad_group')
        self.cur.execute("""
                CREATE OR REPLACE TABLE ad_group_report(
                campaign_id BIGINT,
                campaign_name string,
                ad_group_id BIGINT,
                ad_group_name string
                )
                """)
        self.cur.execute(f'COPY ad_group_report from "{file}" (HEADER true, DELIMITER "\t")')

    def generate_ad_group_id_name_mapping(self):
        """
        Creates a mapping of ad group IDs to their corresponding names and types, based on the input CSV file.
        The mapping is stored in the 'ad_group_id_name_mapping' table.

        Raises:
            Exception: If the SQL execution fails during the creation of the mapping table or when inserting data.
        """
        ddl = """
                CREATE OR REPLACE TABLE ad_group_id_name_mapping (
                    campaign_id BIGINT,
                    campaign_name string,
                    ad_group_id BIGINT,
                    ad_group_name string,
                    ad_group_type string
                )
                """
        self.cur.execute(ddl)

        read_csv_query = f"""
                SELECT 
                    b.campaign_id AS campaign_id, 
                    a.campaign_name AS campaign_name, 
                    a.ad_group_name AS ad_group_name, 
                    a.ad_group_type AS ad_group_type
                FROM (
                    SELECT 
                        DISTINCT CAMPAIGN_NAME AS campaign_name, 
                        GROUP_NAME AS ad_group_name, 
                        GROUP_TYPE AS ad_group_type
                    FROM read_csv('{self.input_file}', delim='\t', header=true)
                    WHERE BIDDING_LEVEL IN ('GROUP', 'HOTEL')
                ) a 
                LEFT JOIN campaign_id_name_mapping b ON (a.campaign_name = b.campaign_name)
                """

        final_query = f"""
                INSERT INTO ad_group_id_name_mapping
                    (SELECT
                        a.campaign_id AS campaign_id,
                        a.campaign_name AS campaign_name,
                        CASE 
                            WHEN b.ad_group_id > 0 THEN b.ad_group_id 
                            ELSE nextval('negative_id_generator') 
                        END AS ad_group_id,
                        a.ad_group_name AS ad_group_name,
                        a.ad_group_type AS ad_group_type
                    FROM ({read_csv_query}) a 
                    LEFT JOIN ad_group_report b
                        ON (a.campaign_name = b.campaign_name AND a.ad_group_name = b.ad_group_name)
                    )
                """
        self.cur.execute(final_query)

    def generate_new_ad_groups(self):
        """
        Generates a list of new ad groups from the ad group ID name mapping table.

        New ad groups are identified by negative ad group IDs. The method selects relevant
        fields, including campaign ID, ad group ID, ad group name, ad group type, and status,
        which is set to 'ENABLED'.

        The resulting data is written to a CSV file named '{account_id}_new_ad_groups.csv'
        located in the '/workspace/bids/' directory.

        Raises:
            Exception: If SQL execution fails or if there is an error during file writing.
        """
        path = f'/workspace/bids/{self.account_id}_new_ad_groups.csv'
        query = f"""
            SELECT
                {self.account_id} as account_id,
                campaign_id AS 'google_ads_entity.ad_group.campaign',
                ad_group_id AS 'google_ads_entity.ad_group.id',
                ad_group_name AS 'google_ads_entity.ad_group.name',
                ad_group_type AS 'google_ads_entity.ad_group.type',
                'ENABLED' AS 'google_ads_entity.ad_group.status'
            FROM ad_group_id_name_mapping
            WHERE ad_group_id < 0
        """
        df = self.cur.execute(query).df()
        if not df.empty:
            df.to_csv(path, index=False, header=True)

    @staticmethod
    def get_report_downloader_query_and_s3_path(run_id: str):
        # customerId is rendered in google-ads report downloader code
        s3_path = '{baseS3Path}/mbt/google-hotel-ads/{runId}/ad_group/{customerId}/file.tsv'.format(
            baseS3Path=os.getenv('S3_PATH'),
            runId=run_id,
            customerId='{customerId}'
        )
        query = """
            SELECT 
                campaign.id, 
                campaign.name, 
                ad_group.id, 
                ad_group.name 
            FROM ad_group 
            WHERE campaign.status IN ('ENABLED', 'PAUSED') AND ad_group.status IN ('ENABLED', 'PAUSED')
        """
        return {'query': ' '.join(query.split()).strip(), 's3Path': s3_path}