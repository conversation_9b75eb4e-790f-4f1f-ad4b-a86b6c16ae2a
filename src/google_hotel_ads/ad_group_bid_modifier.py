import os

from duckdb import DuckDBPyConnection

from src.google_hotel_ads.utils import download_report


class AdGroupBidModifier:
    """
    A class to manage bid modifiers for ad groups, including loading reports and generating bid files for various modifiers.

    Attributes:
        account_id (str): The unique identifier for the account.
        cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        run_id (str): The unique identifier for the current run of the report.
        input_file (str): The file path to the input bid modifier data CSV file.

    Methods:
        generate_bids():
            Orchestrates the process of generating bids by loading necessary reports and writing bid data 
            for different modifiers (date type, booking window, etc.).

        load_ad_group_bid_modifier_report():
            Downloads the ad group bid modifier report and loads it into a DuckDB table named 
            'ad_group_bid_modifier_report'.

        load_ad_group_bid_modifier_input():
            Loads the input bid modifier data from a CSV file into a DuckDB table named 
            'ad_group_bid_modifier_input', joining with the ad group ID-name mapping.

        write_date_type_bids():
            Writes bids related to date selection types to a CSV file.

        write_device_bids():
            Writes bids related to device types to a CSV file.

        write_day_of_week_bids():
            Writes bids related to day-of-week selections to a CSV file.

        write_length_of_stay_bids():
            Writes bids related to length-of-stay modifiers to a CSV file.

        write_booking_window_bids():
            Writes bids related to booking windows to a CSV file.

        write_check_in_date_bids():
            Writes bids related to check-in dates to a CSV file.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroupBidModifier instance with the specified account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}/group.tsv'

    @staticmethod
    def get_report_downloader_query_and_s3_path(run_id: str):
        # customerId is rendered in google-ads report downloader code
        s3_path = '{baseS3Path}/mbt/google-hotel-ads/{runId}/ad_group_bid_modifier/{customerId}/file.tsv'.format(
            baseS3Path=os.getenv('S3_PATH'),
            runId=run_id,
            customerId='{customerId}'
        )
        query ="""
                SELECT 
                  ad_group_bid_modifier.criterion_id, 
                  campaign.id, 
                  campaign.name, 
                  ad_group.id, 
                  ad_group.name, 
                  ad_group_bid_modifier.hotel_advance_booking_window.min_days, 
                  ad_group_bid_modifier.hotel_advance_booking_window.max_days, 
                  ad_group_bid_modifier.hotel_length_of_stay.min_nights, 
                  ad_group_bid_modifier.hotel_length_of_stay.max_nights, 
                  ad_group_bid_modifier.hotel_check_in_date_range.start_date, 
                  ad_group_bid_modifier.hotel_check_in_date_range.end_date, 
                  ad_group_bid_modifier.hotel_date_selection_type.type, 
                  ad_group_bid_modifier.device.type, 
                  ad_group_bid_modifier.hotel_check_in_day.day_of_week, 
                  ad_group_bid_modifier.bid_modifier 
                FROM ad_group_bid_modifier 
                WHERE 
                  campaign.status IN ('ENABLED', 'PAUSED') 
                  AND ad_group.status IN ('ENABLED', 'PAUSED') 
            """
        return {'query': ' '.join(query.split()).strip(), 's3Path': s3_path}

    def generate_bids(self):
        """
        Executes the full process of generating ad group bid modifiers by loading the necessary reports 
        and writing bid data for various types of modifiers (date, device, length of stay, etc.).
        """
        if not os.path.isfile(self.input_file):
            return
        self.load_ad_group_bid_modifier_report()
        self.load_ad_group_bid_modifier_input()
        self.write_date_type_bids()
        self.write_booking_window_bids()
        self.write_length_of_stay_bids()
        self.write_day_of_week_bids()
        self.write_device_bids()
        self.write_check_in_date_bids()

    def load_ad_group_bid_modifier_report(self):
        """
        Downloads the ad group bid modifier report using the run ID and account ID, and loads it into 
        a DuckDB table named 'ad_group_bid_modifier_report'.

        Raises:
            Exception: If the report download fails or SQL execution fails.
        """
        file = download_report(self.run_id, self.account_id, 'ad_group_bid_modifier')
        self.cur.execute("""CREATE OR REPLACE TABLE ad_group_bid_modifier_report(
                    criterion_id BIGINT,
                    campaign_id BIGINT,
                    campaign_name string,
                    ad_group_id BIGINT,
                    ad_group_name string,
                    booking_window_min_days integer,
                    booking_window_max_days integer,
                    hotel_length_of_stay_min_days integer,
                    hotel_length_of_stay_max_days integer,
                    check_in_start_date date,
                    check_in_end_date date,
                    date_selection_type integer,
                    device_type integer,
                    day_of_week integer,
                    bid_modifier float
                    )""")
        self.cur.execute(f'COPY ad_group_bid_modifier_report from "{file}" (HEADER true, DELIMITER "\t")')

    def load_ad_group_bid_modifier_input(self):
        """
        Loads the input bid modifier data from a CSV file into a DuckDB table named 
        'ad_group_bid_modifier_input', joining with the ad group ID-name mapping to retrieve ad group IDs.

        Raises:
            Exception: If SQL execution fails during table creation or data insertion.
        """
        ddl = """
                CREATE OR REPLACE TABLE ad_group_bid_modifier_input(
                campaign_id BIGINT,
                campaign_name string,
                ad_group_id BIGINT,
                ad_group_name string,
                ad_group_type string,
                multiplier_type string,
                multiplier_level string,
                multiplier_value float
                )
                """
        self.cur.execute(ddl)

        read_csv_query = f"""
                SELECT * FROM read_csv('{self.input_file}', delim='\t', header=true)
                """
        final_query = f"""
                INSERT INTO ad_group_bid_modifier_input 
                    (SELECT 
                        a.CAMPAIGN_ID,
                        a.CAMPAIGN_NAME,
                        b.ad_group_id,
                        a.GROUP_NAME,
                        a.GROUP_TYPE,
                        a.BIDMULTIPLIER_TYPE,
                        a.BIDMULTIPLIER_LEVEL,
                        a.BIDMULTIPLIER_VALUE
                    FROM ({read_csv_query}) a 
                    LEFT JOIN ad_group_id_name_mapping b
                        ON (a.CAMPAIGN_NAME = b.campaign_name AND a.GROUP_NAME = b.ad_group_name)
                    )
                """
        self.cur.execute(final_query)

    def write_date_type_bids(self):
        """
        Writes bids related to date selection types to a CSV file, filtering out rows based on the 
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_date_type_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_date_type_bids.csv'
        inner_query = """
        SELECT *, 
        CASE 
            WHEN date_selection_type = 50 THEN 'default' 
            WHEN date_selection_type = 51 THEN 'selected' 
            ELSE 'unspecified' 
        END AS date_type
        FROM ad_group_bid_modifier_report
        WHERE date_selection_type > 0 
        """
        final_query = f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
            a.multiplier_value AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier',
            CASE 
                WHEN b.criterion_id IS NULL THEN 0 
                ELSE b.criterion_id 
            END AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
            CASE 
                WHEN b.criterion_id IS NULL THEN 
                    (CASE 
                        WHEN a.multiplier_level = 'default' THEN 'DEFAULT_SELECTION' 
                        WHEN a.multiplier_level = 'selected' THEN 'USER_SELECTED' 
                        ELSE 'UNSPECIFIED' 
                    END) 
                ELSE '' 
            END AS 'google_ads_entity.ad_group_bid_modifier.hotel_date_selection_type.type'
            FROM ad_group_bid_modifier_input AS a 
            LEFT OUTER JOIN ({inner_query}) b 
            ON (a.campaign_id = b.campaign_id AND a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.date_type)
            WHERE a.multiplier_type = 'DATE_TYPE' AND a.multiplier_value != COALESCE(b.bid_modifier, -1.0)
            """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)

    def write_device_bids(self):
        """
        Writes bids related to device types to a CSV file, filtering out rows based on the multiplier 
        type and comparing with existing bid modifiers.

        The output file is named '{account_id}_device_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_device_bids.csv'
        inner_query = """
                SELECT 
                *, 
                CASE 
                    WHEN device_type = 2 THEN 'mobile' 
                    WHEN device_type = 3 THEN 'tablet' 
                    WHEN device_type = 4 THEN 'desktop' 
                    ELSE 'unspecified' 
                END AS real_device_type
                FROM ad_group_bid_modifier_report WHERE device_type > 0 
                """
        self.cur.execute(
            f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
            a.multiplier_value AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier', 
            CASE 
                WHEN b.criterion_id IS NULL THEN 0 
                ELSE b.criterion_id 
            END AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
            CASE 
                WHEN b.criterion_id IS NULL THEN UPPER(a.multiplier_level) 
                ELSE '' 
            END AS 'google_ads_entity.ad_group_bid_modifier.device.type'
            FROM ad_group_bid_modifier_input a 
            LEFT OUTER JOIN ({inner_query}) b
            ON (a.campaign_id = b.campaign_id AND a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.real_device_type)
            WHERE a.multiplier_type = 'DEVICE_TYPE' AND a.multiplier_value != COALESCE(b.bid_modifier, -1.0)
            """
        ).df().to_csv(path, index=False, header=True)

    def write_day_of_week_bids(self):
        """
        Writes bids related to day-of-week selections to a CSV file, filtering out rows based on the 
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_day_of_week_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_day_of_week_bids.csv'
        inner_query = """
            SELECT *, 
            CASE 
                WHEN day_of_week = 2 THEN 'monday' 
                WHEN day_of_week = 3 THEN 'tuesday' 
                WHEN day_of_week = 4 THEN 'wednesday' 
                WHEN day_of_week = 5 THEN 'thursday' 
                WHEN day_of_week = 6 THEN 'friday' 
                WHEN day_of_week = 7 THEN 'saturday' 
                WHEN day_of_week = 8 THEN 'sunday' 
                ELSE 'unspecified' 
            END AS real_day_of_week
            FROM ad_group_bid_modifier_report WHERE day_of_week > 0 
            """
        self.cur.execute(
            f"""
            SELECT
            {self.account_id} AS account_id,
            a.ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
            a.multiplier_value AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier',
            CASE 
                WHEN b.criterion_id IS NULL THEN 0 
                ELSE b.criterion_id 
            END AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
            CASE 
                WHEN b.criterion_id IS NULL THEN UPPER(a.multiplier_level) 
                ELSE '' 
            END AS 'google_ads_entity.ad_group_bid_modifier.hotel_check_in_day_info.day_of_week'
            FROM ad_group_bid_modifier_input a 
            LEFT OUTER JOIN ({inner_query}) b
            ON (a.campaign_id = b.campaign_id AND a.ad_group_id = b.ad_group_id AND a.multiplier_level = b.real_day_of_week)
            WHERE a.multiplier_type = 'CHECK_IN_DAY_OF_WEEK' AND a.multiplier_value != COALESCE(b.bid_modifier, -1.0)
            """
        ).df().to_csv(path, index=False, header=True)

    def write_length_of_stay_bids(self):
        """
        Writes bids related to length-of-stay modifiers to a CSV file, filtering out rows based on 
        the multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_length_of_stay_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_length_of_stay_bids.csv'
        max_len_days = 30
        max_days_cal_query = f"""
            SELECT
                campaign_id,
                campaign_name,
                ad_group_id,
                ad_group_name,
                multiplier_value AS bid_modifier,
                CAST(multiplier_level AS INTEGER) AS min_days,
                LAG(CAST(multiplier_level AS INTEGER) - 1, 1, {max_len_days})
                    OVER (PARTITION BY ad_group_name, campaign_name ORDER BY CAST(multiplier_level AS INTEGER) DESC) AS max_days
            FROM ad_group_bid_modifier_input
            WHERE multiplier_type = 'LENGTH_OF_STAY_MIN_NIGHTS'
        """
        report_query = """
            SELECT
                criterion_id,
                campaign_id,
                campaign_name,
                ad_group_id,
                ad_group_name,
                bid_modifier,
                hotel_length_of_stay_min_days AS min_days,
                hotel_length_of_stay_max_days AS max_days
            FROM ad_group_bid_modifier_report
            WHERE hotel_length_of_stay_min_days > 0 AND hotel_length_of_stay_max_days > 0 
        """

        final_query = f"""
                SELECT 
                   {self.account_id} AS account_id,
                   b.criterion_id AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
                   a.ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
                   a.bid_modifier AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier',
                   a.min_days AS 'google_ads_entity.ad_group_bid_modifier.hotel_length_of_stay_info.min_nights',
                   a.max_days AS 'google_ads_entity.ad_group_bid_modifier.hotel_length_of_stay_info.max_nights'
                FROM ({max_days_cal_query}) a 
                LEFT JOIN ({report_query}) b 
                ON (a.campaign_id = b.campaign_id AND a.ad_group_id= b.ad_group_id AND a.min_days = b.min_days AND a.max_days = b.max_days)
                WHERE a.bid_modifier != COALESCE(b.bid_modifier, -1.0)
        """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)

    def write_booking_window_bids(self):
        """
        Writes bids related to booking windows to a CSV file, filtering out rows based on the 
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_booking_window_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_booking_window_bids.csv'
        max_len_days = 330
        max_days_cal_query = f"""
        SELECT campaign_id,
               campaign_name,
               ad_group_id,
               ad_group_name,
               multiplier_value AS bid_modifier,
               CAST(multiplier_level AS INTEGER) AS min_days,
               LAG(CAST(multiplier_level AS INTEGER) - 1, 1, {max_len_days} )
                 OVER (
                   PARTITION BY ad_group_name, campaign_name
                   ORDER BY CAST(multiplier_level AS INTEGER) DESC) AS max_days
        FROM ad_group_bid_modifier_input
        WHERE multiplier_type = 'BOOKING_WINDOW_MIN_DAYS'
        """

        report_query = f"""
                    SELECT 
                          criterion_id,
                          campaign_id,
                          campaign_name,
                          ad_group_id,
                          ad_group_name,
                          bid_modifier,
                          booking_window_min_days AS min_days,
                          booking_window_max_days AS max_days
                   FROM ad_group_bid_modifier_report
                   WHERE hotel_length_of_stay_min_days = 0 AND hotel_length_of_stay_max_days = 0 
                            AND check_in_start_date IS NULL 
                            AND check_in_end_date IS NULL 
                            AND date_selection_type = 0 
                            AND device_type = 0 
                            AND day_of_week = 0
        """

        final_query = f"""
                    SELECT 
                        {self.account_id} AS account_id,
                        b.criterion_id AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
                        a.ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
                        a.bid_modifier AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier',
                        a.min_days AS 'google_ads_entity.ad_group_bid_modifier.hotel_advance_booking_window_info.min_days',
                        a.max_days AS 'google_ads_entity.ad_group_bid_modifier.hotel_advance_booking_window_info.max_days'
                    FROM ({max_days_cal_query}) a 
                    LEFT JOIN ({report_query}) b 
                    ON (a.campaign_id = b.campaign_id AND a.ad_group_id= b.ad_group_id AND a.min_days = b.min_days AND a.max_days = b.max_days)
                    WHERE a.bid_modifier != COALESCE(b.bid_modifier, -1.0)
                """
        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)

    def write_check_in_date_bids(self):
        """
        Writes bids related to check-in dates to a CSV file, filtering out rows based on the 
        multiplier type and comparing with existing bid modifiers.

        The output file is named '{account_id}_check_in_date_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        path = f'/workspace/bids/{self.account_id}_check_in_date_bids.csv'
        delete_query = f"""
                    SELECT
                        {self.account_id} AS account_id,
                        ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
                        '' AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier', 
                        criterion_id AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
                        'REMOVED' AS 'google_ads_entity.ad_group_bid_modifier.status',
                        '' AS 'google_ads_entity.ad_group_bid_modifier.hotel_check_in_date_range_info.start_date',
                        '' AS 'google_ads_entity.ad_group_bid_modifier.hotel_check_in_date_range_info.end_date' 
                    FROM ad_group_bid_modifier_report
                    WHERE check_in_start_date IS NOT NULL AND check_in_end_date IS NOT NULL
                """

        # Query to select new or updated bid modifiers
        create_query = f"""
                    SELECT
                        {self.account_id} AS account_id,
                        ad_group_id AS 'google_ads_entity.ad_group_bid_modifier.ad_group',
                        multiplier_value AS 'google_ads_entity.ad_group_bid_modifier.bid_modifier', 
                        nextval('negative_id_generator') AS 'google_ads_entity.ad_group_bid_modifier.criterion_id',
                        'ENABLED' AS 'google_ads_entity.ad_group_bid_modifier.status',
                        greatest(cast(SPLIT_PART(multiplier_level, ':', 1) as date), current_date) AS 'google_ads_entity.ad_group_bid_modifier.hotel_check_in_date_range_info.start_date',
                        SPLIT_PART(multiplier_level, ':', 2) AS 'google_ads_entity.ad_group_bid_modifier.hotel_check_in_date_range_info.end_date'
                    FROM ad_group_bid_modifier_input
                    WHERE multiplier_type = 'CHECK_IN_DATE' and cast(SPLIT_PART(multiplier_level, ':', 2) as date) >= current_date
                """

        # Combine delete and create queries and execute
        final_query = f"""
                    SELECT * FROM (
                        ({delete_query})
                        UNION
                        ({create_query})
                    ) ORDER BY 5 DESC
                """

        self.cur.execute(final_query).df().to_csv(path, index=False, header=True)
