import os

from src.splitter import config
from src.utils import aws


def download_report(run_id, account, resource) -> str:
    destination = f'{config.SPLITTER_OUTPUT_PATH}/{account}/{resource}_report.tsv'
    if os.getenv('ENV', 'DEV').lower() != 'dev':
        aws.s3_cp(
            origin=f'{os.getenv("S3_PATH")}/mbt/google-hotel-ads/{run_id}/{resource}/{account}/file.tsv',
            destination=destination,
        )
    return destination
