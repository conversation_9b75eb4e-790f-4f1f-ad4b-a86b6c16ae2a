import os
from duckdb import DuckDBPyConnection
from src.google_hotel_ads.utils import download_report


class AdGroupAd:
    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroupAd instance with the given account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id

    def generate_bids(self):
        """
        Orchestrates the process of generating bids by loading the ad group ad report
        and generating the ad group ad resource.
        """
        self.load_ad_group_ad_report()
        self.generate_ad_group_ad_resource()

    def load_ad_group_ad_report(self):
        """
        Downloads the ad group ad report using the run ID and account ID, and loads it into a DuckDB table
        named 'ad_group_ad_report'. The table includes campaign IDs, campaign names, ad group IDs, and ad group names.

        Raises:
            Exception: If the report download fails or SQL execution fails.
        """
        file = download_report(self.run_id, self.account_id, 'ad_group_ad')
        self.cur.execute("""
                CREATE OR REPLACE TABLE ad_group_ad_report(
                ad_group_ad_resource string,
                campaign_id BIGINT,
                campaign_name string,
                ad_group_id BIGINT,
                ad_group_name string
                )
                """)
        self.cur.execute(f'COPY ad_group_ad_report from "{file}" (HEADER true, DELIMITER "\t")')

    def generate_ad_group_ad_resource(self):
        """
        Generates the ad group ad resource by querying the ad group ad report and saving the result
        as a CSV file in the specified path.

        The CSV file will contain details including account ID, ad group ID, status, and a dummy field.

        Raises:
            Exception: If the SQL execution fails or the DataFrame conversion to CSV fails.
        """
        path = f'/workspace/bids/{self.account_id}_new_ad_group_ads.csv'
        final_query = f"""SELECT
                                {self.account_id} as account_id,
                                a.ad_group_id as 'google_ads_entity.ad_group_ad.ad_group',
                               'ENABLED' as 'google_ads_entity.ad_group_ad.status',
                               'dummy' as 'google_ads_entity.ad_group_ad.ad.hotel_ad.dummy_field'
                           FROM ad_group_id_name_mapping a 
                           LEFT JOIN ad_group_ad_report b
                               ON (a.campaign_name = b.campaign_name AND a.ad_group_name = b.ad_group_name)
                            WHERE b.ad_group_ad_resource is null
                       """
        df = self.cur.execute(final_query).df()
        if not df.empty:
            df.to_csv(path, index=False, header=True)

    @staticmethod
    def get_report_downloader_query_and_s3_path(run_id: str):
        """
        Constructs the SQL query to retrieve ad group ad information and generates the corresponding
        S3 path for the downloaded report.

        Args:
            run_id (str): The unique identifier for the current run.

        Returns:
            dict: A dictionary containing the SQL query string and the S3 path for the downloaded report.
        """
        s3_path = '{baseS3Path}/mbt/google-hotel-ads/{runId}/ad_group_ad/{customerId}/file.tsv'.format(
            baseS3Path=os.getenv('S3_PATH'),
            runId=run_id,
            customerId='{customerId}'
        )
        query = """
                SELECT 
                  ad_group_ad.resource_name, 
                  campaign.id, 
                  campaign.name, 
                  ad_group.id, 
                  ad_group.name 
                FROM ad_group_ad 
                WHERE 
                  ad_group_ad.ad.type = 'HOTEL_AD' 
                  AND ad_group_ad.status IN ('ENABLED', 'PAUSED') 
                  AND ad_group.status IN ('ENABLED', 'PAUSED') 
                  AND campaign.status IN ('ENABLED', 'PAUSED') 
            """
        return {'query': ' '.join(query.split()).strip(), 's3Path': s3_path}
