import os

import pandas as pd
from duckdb import DuckDBPyConnection

from src.google_hotel_ads.utils import download_report


class AdGroupCriterion:
    """
    A class to manage ad group criteria, including loading hotel reports, generating parent criterion mappings,
    and writing hotel bids based on input data.

    Attributes:
        account_id (str): The unique identifier for the account.
        cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        run_id (str): The unique identifier for the current run of the report.
        input_file (str): The file path to the input hotel bids data CSV file.

    Methods:
        generate_bids():
            Orchestrates the process of generating hotel bids by loading necessary reports, mapping parent criteria,
            loading input bids, and writing final bid data.

        load_hotel_report():
            Downloads the hotel report and loads it into a DuckDB table named 'hotel_report'.

        generate_ad_group_parent_criterion_mapping():
            Creates a mapping of ad group parent criteria, inserting data into a DuckDB table named
            'ad_group_parent_criterion_mapping'.

        load_input_bids():
            Loads input bid data from a CSV file into a DuckDB table named 'hotel_input', joining with the
            ad group parent criterion mapping.

        write_hotel_bids():
            Writes hotel bid data to a CSV file, filtering based on paused status and comparing with existing
            bid data from the hotel report.

    Raises:
        Exception: If SQL execution fails during report loading, data insertion, or bid writing.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the AdGroupCriterion instance with the specified account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.run_id = run_id
        self.cur = cur
        self.input_file = f'/workspace/inputs/{account_id}/hotel.tsv'

    @staticmethod
    def get_report_downloader_query_and_s3_path(run_id: str):
        # customerId is rendered in google-ads report downloader code
        s3_path = '{baseS3Path}/mbt/google-hotel-ads/{runId}/hotel/{customerId}/file.tsv'.format(
            baseS3Path=os.getenv('S3_PATH'),
            runId=run_id,
            customerId='{customerId}'
        )
        query = """
                SELECT 
                  ad_group_criterion.criterion_id, 
                  campaign.id, 
                  campaign.name, 
                  ad_group.id, 
                  ad_group.name, 
                  ad_group_criterion.cpc_bid_micros, 
                  ad_group_criterion.listing_group.case_value.hotel_id.value, 
                  ad_group_criterion.listing_group.type, 
                  ad_group_criterion.listing_group.parent_ad_group_criterion 
                FROM ad_group_criterion 
                WHERE 
                  campaign.status IN ('ENABLED', 'PAUSED') 
                  AND ad_group.status IN ('ENABLED', 'PAUSED') 
                  AND ad_group_criterion.status IN ('ENABLED', 'PAUSED') 
            """
        return {'query': ' '.join(query.split()).strip(), 's3Path': s3_path}

    def generate_bids(self):
        """
        Executes the full process of generating hotel bids by loading reports, mapping parent criteria,
        loading input bids, and writing final bid data.
        """
        if not os.path.isfile(self.input_file):
            return
        self.load_hotel_report()
        self.generate_ad_group_parent_criterion_mapping()
        self.load_input_bids()
        self.write_hotel_bids()
        self.generate_parent_ad_group_criterion_bids()

    def load_hotel_report(self):
        """
        Downloads the hotel report using the run ID and account ID, and loads it into a DuckDB table named
        'hotel_report'.

        Raises:
            Exception: If the report download fails or SQL execution fails.
        """
        file = download_report(self.run_id, self.account_id, 'hotel')
        self.cur.execute("""CREATE OR REPLACE TABLE hotel_report(
                    criterion_id BIGINT,
                    campaign_id BIGINT,
                    campaign_name string,
                    ad_group_id BIGINT,
                    ad_group_name string,
                    base_bid BIGINT,
                    hotel_id BIGINT,
                    hotel_type integer,
                    parent_ad_group_criterion string
                    )""")
        self.cur.execute(f'COPY hotel_report from "{file}" (HEADER true, DELIMITER "\t")')

    def generate_ad_group_parent_criterion_mapping(self):
        """
        Creates a mapping of ad group parent criteria and loads it into a DuckDB table named
        'ad_group_parent_criterion_mapping'.

        Raises:
            Exception: If SQL execution fails during table creation or data insertion.
        """
        ddl = """
        CREATE OR REPLACE TABLE ad_group_parent_criterion_mapping(
        campaign_id BIGINT,
        campaign_name string,
        ad_group_id BIGINT,
        ad_group_name string,
        ad_group_type string,
        parent_criterion_id BIGINT
        )
        """
        self.cur.execute(ddl)
        final_query = f"""
        INSERT INTO ad_group_parent_criterion_mapping (
        SELECT 
            a.*, 
            CASE WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') ELSE b.criterion_id END AS parent_criterion_id
            FROM ad_group_id_name_mapping a 
            LEFT JOIN (
                SELECT criterion_id, campaign_id, ad_group_id 
                FROM hotel_report 
                WHERE hotel_type = 2 
            ) b
            ON (a.campaign_id = b.campaign_id AND a.ad_group_id = b.ad_group_id)
        )
        """
        self.cur.execute(final_query)

    def load_input_bids(self):
        """
        Loads input bid data from a CSV file into a DuckDB table named 'hotel_input', joining with the
        ad group parent criterion mapping.

        Raises:
            Exception: If SQL execution fails during table creation or data insertion.
        """
        ddl = """ CREATE OR REPLACE TABLE hotel_input(
            hotel_id BIGINT,
            campaign_id BIGINT,
            campaign_name string,
            ad_group_id BIGINT,
            ad_group_name string,
            base_bid BIGINT,
            is_paused string,
            parent_criterion_id BIGINT
            )"""
        self.cur.execute(ddl)
        read_csv_query = f"""
                SELECT * FROM read_csv('{self.input_file}', delim='\t', header=true)
        """
        final_query = f"""
        INSERT INTO hotel_input 
        (SELECT
            a.HOTEL_ID, 
            b.CAMPAIGN_ID, 
            a.CAMPAIGN_NAME, 
            b.ad_group_id, 
            a.GROUP_NAME, 
            round(a.BASE_BID, 2) * (10**6) as base_bid,
            a.IS_PAUSED,
            b.parent_criterion_id 
            FROM ({read_csv_query}) a 
            LEFT JOIN ad_group_parent_criterion_mapping b 
            ON (a.CAMPAIGN_NAME = b.campaign_name AND a.GROUP_NAME = b.ad_group_name)
            )
            """
        self.cur.execute(final_query)

    def write_hotel_bids(self):
        """
        Writes hotel bid data to a CSV file, filtering out paused bids and comparing the base bid values
        with existing data from the hotel report.

        The output file is named '{account_id}_hotel_bids.csv' and paused bids are written to
        '{account_id}_paused_hotel_bids.csv'.

        Raises:
            Exception: If SQL execution fails during data selection or file writing.
        """
        hotel_bids_path = f'/workspace/bids/{self.account_id}_hotel_bids.csv'

        un_paused_query = f"""SELECT
            {self.account_id} as account_id,
            CASE WHEN b.criterion_id IS NULL THEN nextval('negative_id_generator') ELSE CAST(b.criterion_id AS BIGINT) END AS 'google_ads_entity.ad_group_criterion.criterion_id',
            a.hotel_id as 'google_ads_entity.ad_group_criterion.listing_group.case_value.hotel_id_info.value',
            a.ad_group_id as 'google_ads_entity.ad_group_criterion.ad_group',
            a.base_bid AS 'google_ads_entity.ad_group_criterion.cpc_bid_micros',
            a.parent_criterion_id AS 'google_ads_entity.ad_group_criterion.listing_group.parent_ad_group_criterion',
            'UNIT' as 'google_ads_entity.ad_group_criterion.listing_group.type',
            'ENABLED' as 'google_ads_entity.ad_group_criterion.status' 
            FROM hotel_input a 
            LEFT JOIN hotel_report b 
            ON (a.hotel_id = b.hotel_id AND a.ad_group_id = b.ad_group_id AND a.campaign_id = b.campaign_id) 
            WHERE a.is_paused = 'false' AND a.base_bid != COALESCE(b.base_bid, 0)
            """

        paused_query = f"""SELECT
            {self.account_id} AS account_id,
            CAST(b.criterion_id AS BIGINT) AS 'google_ads_entity.ad_group_criterion.criterion_id',
            a.hotel_id as 'google_ads_entity.ad_group_criterion.listing_group.case_value.hotel_id_info.value', 
            b.ad_group_id as 'google_ads_entity.ad_group_criterion.ad_group',
            '' AS 'google_ads_entity.ad_group_criterion.cpc_bid_micros',
            '' AS 'google_ads_entity.ad_group_criterion.listing_group.parent_ad_group_criterion',
            '' AS 'google_ads_entity.ad_group_criterion.listing_group.type',
            'REMOVED' as 'google_ads_entity.ad_group_criterion.status',
            FROM hotel_input a
            INNER JOIN hotel_report b
            ON (a.hotel_id = b.hotel_id AND a.ad_group_id = b.ad_group_id and a.campaign_id = b.campaign_id)
            WHERE a.is_paused != 'false'
            """

        moved_hotels_query = f""" SELECT
                    {self.account_id} AS account_id,
                    CAST(b.criterion_id AS BIGINT) AS 'google_ads_entity.ad_group_criterion.criterion_id',
                    a.hotel_id as 'google_ads_entity.ad_group_criterion.listing_group.case_value.hotel_id_info.value', 
                    b.ad_group_id as 'google_ads_entity.ad_group_criterion.ad_group',
                    '' AS 'google_ads_entity.ad_group_criterion.cpc_bid_micros',
                    '' AS 'google_ads_entity.ad_group_criterion.listing_group.parent_ad_group_criterion',
                    '' AS 'google_ads_entity.ad_group_criterion.listing_group.type',
                    'REMOVED' as 'google_ads_entity.ad_group_criterion.status',
                    FROM hotel_input a INNER JOIN hotel_report b ON (a.hotel_id = b.hotel_id AND a.campaign_id = b.campaign_id AND a.ad_group_id != b.ad_group_id)
                """
        final_query = f"""
        select * from (
        ({un_paused_query})
        UNION
        ({paused_query})
        UNION
        ({moved_hotels_query})
        )
        order by 4
        """
        self.cur.execute(final_query).df().to_csv(hotel_bids_path, index=False, header=True)

    def generate_parent_ad_group_criterion_bids(self):
        path = f'/workspace/bids/{self.account_id}_parent_ad_group_criterions.csv'

        query = f"""
            select 
                {self.account_id} AS account_id,
                parent_criterion_id AS 'google_ads_entity.listing_group_parent_criterion.criterion_id',
                ad_group_id as 'google_ads_entity.listing_group_parent_criterion.ad_group',
                'SUBDIVISION' as 'google_ads_entity.listing_group_parent_criterion.listing_group.type',
                'ENABLED' as 'google_ads_entity.listing_group_parent_criterion.status',
                'False' as 'google_ads_entity.listing_group_parent_criterion.negative',
                '' as 'google_ads_entity.listing_group_parent_criterion.listing_group.parent_ad_group_criterion'
                FROM ad_group_parent_criterion_mapping
                where parent_criterion_id < 0 
        """

        def add_other_node(row, temp_id):
            new_row = row.copy()
            new_row['google_ads_entity.listing_group_parent_criterion.negative'] = 'True'
            new_row['google_ads_entity.listing_group_parent_criterion.listing_group.type'] = 'UNIT'
            new_row['google_ads_entity.listing_group_parent_criterion.listing_group.parent_ad_group_criterion'] = row['google_ads_entity.listing_group_parent_criterion.criterion_id']
            new_row['google_ads_entity.listing_group_parent_criterion.criterion_id'] = temp_id
            return new_row

        sub_division_df = self.cur.execute(query).df()
        temp_id = int(self.cur.execute("""select nextval('negative_id_generator')""").fetchone()[0])
        others_node_rows = []
        for index, row in sub_division_df.iterrows():
            result = add_other_node(row, temp_id)
            temp_id -= 1
            others_node_rows.append(result)
        others_node_df = pd.DataFrame(others_node_rows, columns=sub_division_df.columns)

        # send subdivision and other node 1 after 1 so that both end up in same mutate req
        combined_rows = []
        for index in range(len(sub_division_df)):
            combined_rows.append(sub_division_df.iloc[index])
            combined_rows.append(others_node_df.iloc[index])

        final_df = pd.DataFrame(combined_rows).reset_index(drop=True)
        final_df.to_csv(path, index=False, header=True)
