# Compile & Run

### Install Docker

To get started, install Docker by following the official installation guide:

[Docker Installation Guide](https://docs.docker.com/engine/install/)

### Build the Project

Once Docker is installed, build the project with the following command:

```bash
docker compose build
```


### Testing Locally

If you want to test the application locally without an internet connection, follow these steps:

1. **Modify Input Files**

   - Navigate to the `tests/google_hotel_ads/data/inputs/` directory.
   - Edit or add new rows to the input files as needed.

   For example, to test a campaign for a new resource, add new rows to the file:

   ```plaintext
   tests/google_hotel_ads/data/inputs/1166058717/campaign.tsv

Run this command:
```bash
docker compose run workflow pytest -vvvv tests/google_hotel_ads/
```
After running the command, a new directory called `/workspace/bids/` will be created. You can check this directory to verify that the bids for the new resource you are testing have been generated correctly.

Once the logic is confirmed to be correct, you can write a test function in `tests/google_hotel_ads/test_gha.py` to assert the bids. This will ensure that any future changes to the logic will cause the test to fail if the bids are incorrect.

### Testing with Production Data

Details for testing with production data will be available soon.
