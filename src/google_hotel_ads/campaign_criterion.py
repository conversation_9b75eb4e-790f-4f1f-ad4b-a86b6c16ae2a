import os

from duckdb import DuckDBPyConnection

from src.google_hotel_ads.utils import download_report


class CampaignCriterion:
    """
    A class to manage campaign criteria and generate user country bids for a specified account.

    Attributes:
        account_id (str): The unique identifier for the account.
        cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        run_id (str): The unique identifier for the current run of the report.
        input_file (str): The file path to the input campaign criteria CSV file.

    Methods:
        generate_bids():
            Orchestrates the process of generating user country bids by loading necessary data.

        load_geo_report():
            Downloads the geographical report and loads it into a DuckDB table.

        load_input_campaign_criterion():
            Loads campaign criteria from a specified CSV file into a DuckDB table.

        write_user_country_bids():
            Writes the generated user country bids to a CSV file.
    """

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        """
        Initializes the CampaignCriterion instance with the given account ID, run ID, and database connection.

        Args:
            account_id (str): The unique identifier for the account.
            run_id (str): The unique identifier for the current run.
            cur (DuckDBPyConnection): A DuckDB connection object for executing SQL queries.
        """
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}/campaign.tsv'

    def generate_bids(self):
        """
        Executes the full process of generating user country bids, which includes loading the geo report,
        loading the input campaign criteria, and writing the user country bids to a CSV file.
        """
        self.load_geo_report()
        self.load_input_campaign_criterion()
        self.write_user_country_bids()
        self.write_campaign_bids()
        self.write_campaign_criterion_audience_targeting()

    def load_geo_report(self):
        """
        Downloads the geographical report using the run ID and account ID, and loads it into a DuckDB table
        named 'geo_report'. The table includes country, ID, and type information.

        Raises:
            Exception: If the report download fails or SQL execution fails.
        """
        file = '/app/resources/google_geo_targets.tsv'
        self.cur.execute("CREATE OR REPLACE TABLE geo_report(country string,id integer, type string)")
        self.cur.execute(f'COPY geo_report from "{file}" (HEADER true, DELIMITER "\t")')

    def load_input_campaign_criterion(self):
        """
        Loads campaign criteria from a CSV file located at the input file path into a DuckDB table
        named 'campaign_criterion_input'. The table consists of campaign IDs, names, multiplier types,
        levels, and values.

        Raises:
            Exception: If the SQL execution fails while creating the table or loading data.
        """
        self.cur.execute("""CREATE OR REPLACE TABLE campaign_criterion_input(
                    campaign_id BIGINT,
                    campaign_name string,
                    multiplier_type string,
                    multiplier_level string,
                    multiplier_value float
                    )""")
        self.cur.execute(f'COPY campaign_criterion_input from "{self.input_file}" (HEADER true, DELIMITER "\t")')

    def write_user_country_bids(self):
        """
        Generates user country bids based on the loaded campaign criteria and geographical data,
        and writes the results to a CSV file. The output file is named '{account_id}_user_country_bids.csv'
        and is stored in the '/workspace/bids' directory.

        Raises:
            Exception: If the SQL execution fails or if there are issues writing the CSV file.
        """
        os.makedirs('/workspace/bids', exist_ok=True)
        path = f'/workspace/bids/{self.account_id}_user_country_bids.csv'
        df = self.cur.execute(f"""
                        SELECT
                           {self.account_id} AS account_id,
                           a.campaign_id AS 'google_ads_entity.campaign_criterion.campaign',
                           a.bid_modifier AS 'google_ads_entity.campaign_criterion.bid_modifier',
                           COALESCE(b.id, 0) AS 'google_ads_entity.campaign_criterion.location.geo_target_constant'
                           FROM 
                           (SELECT 
                                b.campaign_id AS campaign_id, 
                                a.multiplier_value AS bid_modifier, 
                                a.multiplier_level AS user_country 
                                FROM campaign_criterion_input a 
                                LEFT JOIN campaign_id_name_mapping b 
                                ON a.campaign_name = b.campaign_name 
                                WHERE a.multiplier_type = 'USER_COUNTRY') a 
                           LEFT JOIN geo_report b ON (a.user_country = b.country)
                           """
                         ).df()
        if not df.empty:
            df.to_csv(path, index=False, header=True, sep=",")

    def write_campaign_bids(self):
        """
        Generates target ROAS bids based on the loaded campaign criteria,
        and writes the results to a CSV file. The output file is named '{account_id}_campaign_bids.csv'
        and is stored in the '/workspace/bids' directory.
        Raises:
            Exception: If the SQL execution fails or if there are issues writing the CSV file.
        """
        os.makedirs('/workspace/bids', exist_ok=True)
        path = f'/workspace/bids/{self.account_id}_campaign_bids.csv'
        get_target_roas_query = f"""
                                SELECT
                                    {self.account_id} AS account_id,
                                    CAMPAIGN_ID AS 'google_ads_entity.campaign.id',
                                    CASE
                                        WHEN BIDMULTIPLIER_TYPE = 'TARGET_CPA' THEN 'COMMISSION'
                                        WHEN BIDMULTIPLIER_TYPE = 'TARGET_ROAS' THEN 'MAXIMIZE_CONVERSION_VALUE'
                                    END AS 'google_ads_entity.campaign.bidding_strategy_type',
                                    CASE
                                        WHEN BIDMULTIPLIER_TYPE = 'TARGET_CPA' THEN CAST(BIDMULTIPLIER_VALUE * 10**4 AS BIGINT)
                                    END AS 'google_ads_entity.campaign.commission.commission_rate_micros',
                                    CASE
                                        WHEN BIDMULTIPLIER_TYPE = 'TARGET_ROAS' THEN ROUND(BIDMULTIPLIER_VALUE / 100.0, 2)
                                    END AS 'google_ads_entity.campaign.maximize_conversion_value.target_roas'
                                FROM read_csv('{self.input_file}', delim='\t', header=true)
                                WHERE BIDMULTIPLIER_TYPE IN ('TARGET_CPA', 'TARGET_ROAS')
                                """
        df = self.cur.execute(get_target_roas_query).df()
        if not df.empty:
            df.to_csv(path, index=False, header=True, sep=",")
            
            
    def write_campaign_criterion_audience_targeting(self):
        """
        Generates GHA audience bids based on the loaded campaign criteria,
        and writes the results to a CSV file. The output file is named '{account_id}_campaign_criterion_audience_targeting.csv'
        and is stored in the '/workspace/bids' directory.
        Raises:
            Exception: If the SQL execution fails or if there are issues writing the CSV file.
        """
        os.makedirs('/workspace/bids', exist_ok=True)
        path = f'/workspace/bids/{self.account_id}_campaign_criterion_audience_targeting.csv'
        get_audience_targeting_query = f"""
                                SELECT
                                    {self.account_id} AS account_id,
                                    campaign_id AS 'google_ads_entity.campaign_criterion.campaign',
                                    BIDMULTIPLIER_LEVEL AS 'google_ads_entity.campaign_criterion.user_list.user_list',
                                    BIDMULTIPLIER_VALUE AS 'google_ads_entity.campaign_criterion.bid_modifier'
                                FROM read_csv('{self.input_file}', delim='\t', header=true)
                                WHERE BIDMULTIPLIER_TYPE IN ('AUDIENCE')
                                """
        df = self.cur.execute(get_audience_targeting_query).df()
        if not df.empty:
            df.to_csv(path, index=False, header=True, sep=",")
