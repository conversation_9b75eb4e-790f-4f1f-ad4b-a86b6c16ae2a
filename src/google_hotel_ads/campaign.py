import os

from duckdb import DuckDBPyConnection

from src.google_hotel_ads.utils import download_report


class Campaign:
    account_id: str
    cur: DuckDBPyConnection
    run_id: str
    input_file: str

    def __init__(self, account_id: str, run_id: str, cur: DuckDBPyConnection):
        self.account_id = account_id
        self.cur = cur
        self.run_id = run_id
        self.input_file = f'/workspace/inputs/{account_id}.tsv'

    def generate_bids(self):
        self.load_campaign_report()
        self.generate_campaign_id_mapping()

    @staticmethod
    def get_report_downloader_query_and_s3_path(run_id: str):
        # customerId is rendered in google-ads report downloader code
        s3_path = '{baseS3Path}/mbt/google-hotel-ads/{runId}/campaign/{customerId}/file.tsv'.format(
            baseS3Path=os.getenv('S3_PATH'),
            runId=run_id,
            customerId='{customerId}'
        )
        query = """
                    SELECT 
                        campaign.id, 
                        campaign.name, 
                        campaign.status, 
                        campaign.campaign_budget, 
                        campaign.bidding_strategy_type 
                    FROM campaign 
                    WHERE campaign.status IN ('ENABLED', 'PAUSED')
                """
        return {'query': ' '.join(query.split()).strip(), 's3Path': s3_path}

    def load_campaign_report(self):
        file = download_report(self.run_id, self.account_id, 'campaign' )
        self.cur.execute("""
                        CREATE OR REPLACE TABLE campaign_report(
                        campaign_id BIGINT,
                        campaign_name string,
                        campaign_status integer,
                        campaign_budget string,
                        campaign_bidding_strategy_type integer
                        )
                        """)
        self.cur.execute(f'COPY campaign_report from "{file}" (HEADER true, DELIMITER "\t")')

    def generate_campaign_id_mapping(self):
        ddl = """
                    CREATE OR REPLACE TABLE campaign_id_name_mapping (
                        campaign_id BIGINT,
                        campaign_name string,
                        bid_strategy_type string,
                        enhanced_cpc string,
        )"""
        self.cur.execute(ddl)
        read_csv_query = f"""
                        select distinct CAMPAIGN_ID, CAMPAIGN_NAME, BID_STRATEGY_TYPE, ENHANCED_CPC from read_csv('{self.input_file}', delim='\t', header=true)
                        where BIDDING_LEVEL in ('CAMPAIGN')
                        """
        final_query = f"""
                        insert into campaign_id_name_mapping
                            (select
                                case when b.campaign_id > 0 then b.campaign_id else nextval('negative_id_generator') end as campaign_id,
                                a.CAMPAIGN_NAME as campaign_name,
                                a.BID_STRATEGY_TYPE as bid_strategy_type,
                                a.ENHANCED_CPC as enhanced_cpc
                                from ({read_csv_query}) a left join campaign_report b
                                    on (a.CAMPAIGN_NAME = b.campaign_name)
                                )
                        """
        self.cur.execute(final_query)
