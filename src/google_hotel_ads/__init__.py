import json

from duckdb import DuckDBPyConnection

from src.google_hotel_ads.ad_group import AdGroup
from src.google_hotel_ads.ad_group_ad import AdGroupAd
from src.google_hotel_ads.ad_group_bid_modifier import AdGroupBidModifier
from src.google_hotel_ads.ad_group_criterion import AdGroupCriterion
from src.google_hotel_ads.campaign import Campaign
from src.google_hotel_ads.campaign_criterion import CampaignCriterion


class Generator:
    run_id: str
    account: str
    con: DuckDBPyConnection

    def __init__(self, account_id, run_id, con):
        self.account = account_id
        self.run_id = run_id
        self.con = con

    def generate_bids(self):
        campaign_generator = Campaign(self.account, self.run_id, self.con)
        campaign_generator.generate_bids()
        campaign_criterion_generator = CampaignCriterion(self.account, self.run_id, self.con)
        campaign_criterion_generator.generate_bids()
        ad_group_generator = AdGroup(self.account, self.run_id, self.con)
        ad_group_generator.generate_bids()
        ad_group_ad = AdGroupAd(self.account, self.run_id, self.con)
        ad_group_ad.generate_bids()
        ad_group_bid_modifier_generator = AdGroupBidModifier(self.account, self.run_id, self.con)
        ad_group_bid_modifier_generator.generate_bids()
        ad_group_criterion_generator = AdGroupCriterion(self.account, self.run_id, self.con)
        ad_group_criterion_generator.generate_bids()
        self.con.close()

    @staticmethod
    def generate_reports_to_download(run_id):
        reports = [Campaign.get_report_downloader_query_and_s3_path(run_id),
                   AdGroup.get_report_downloader_query_and_s3_path(run_id),
                   AdGroupCriterion.get_report_downloader_query_and_s3_path(run_id),
                   AdGroupBidModifier.get_report_downloader_query_and_s3_path(run_id),
                   AdGroupAd.get_report_downloader_query_and_s3_path(run_id)]

        with open('/workspace/tmp/report_queries_and_s3_paths.json', 'w') as f:
            json.dump(reports, f)
