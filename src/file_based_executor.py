import argparse
import json

import loguru

from common.config import setup_config
from common.config import setup_sftp
from skyscanner import SkyscannerWorkflow
from vacation_renter import VacationRenterWorkflow
from trip_advisor import TAPartnerWorkflow
from trivago import TVGOPartnerWorkflow
from cheap_flights import CheapFlightsWorkflow
from kayak import KayakPartnerWorkflow, NonCoreKayakPartnerWorkflow
from kayak.cars import KayakCarsPartnerWorkflow
from hometogo import HomeToGoWorkflow
from src.utils import aws

parser = argparse.ArgumentParser()
parser.add_argument(
    '--inputS3Path', type=str, help='input bids s3 path or file based on how your algo expects',
)
parser.add_argument(
    '--channel', type=str, help='marketing channel to run for',
)
parser.add_argument(
    '--runId', type=str, help='The unique id for the run',
)
parser.add_argument(
    '--parameters', type=str, help='run parameters - json string',
)

args = parser.parse_args()
input_s3_path = args.inputS3Path
channel = args.channel.replace('-', '_')
run_id = args.runId
parameters = json.loads(args.parameters)
brand = parameters.get("brand", "")

loguru.logger.info('Setting up config')
config = setup_config(run_id=run_id, channel=channel, input_s3_path=input_s3_path, brand=brand)

# Sync bids locally
if config.sys_conf.env in ['prod', 'dev']:
    loguru.logger.info(f"Downloading bids data from {config.run_conf.input_s3_path} to {config.sys_conf.bids_local_path}")
    aws.s3_sync(
        origin=config.run_conf.input_s3_path,
        destination=config.sys_conf.bids_local_path,
    )

if config.run_conf.channel == 'trip_advisor':
    workflow = TAPartnerWorkflow(config)
    workflow.generate_bids()

if config.run_conf.channel == 'trivago':
    workflow = TVGOPartnerWorkflow(config)
    workflow.generate_bids()

if config.run_conf.channel == 'cheap_flights':
    workflow = CheapFlightsWorkflow(config, setup_sftp())
    workflow.generate_bids()

if config.run_conf.channel == 'kayak':
    lob = parameters.get("lob", "").lower()
    placement = parameters.get("placement", "").lower()
    if lob == 'cars':
        workflow = KayakCarsPartnerWorkflow(config)
    elif lob == 'flights' and placement == 'fsr':
        workflow = NonCoreKayakPartnerWorkflow(config, setup_sftp())
    else:
        workflow = KayakPartnerWorkflow(config)
    workflow.generate_bids()

if config.run_conf.channel == 'hometogo':
    workflow = HomeToGoWorkflow(config, setup_sftp())
    workflow.generate_bids()

if config.run_conf.channel == 'vacation_renter':
    workflow = VacationRenterWorkflow(config, setup_sftp())
    workflow.generate_bids()

if config.run_conf.channel == 'skyscanner':
    workflow = SkyscannerWorkflow(config, setup_sftp())
    workflow.generate_bids()