import csv
import glob
import os
from datetime import datetime
from pathlib import Path

import ibis
import loguru
import trino
import urllib3
import yaml
from ibis.backends import duckdb as ibis_duck
from ibis.backends import trino as ibis_trino
from marketing_pb.entity.v1.entity_pb2 import MarketingEntity
from marketing_pb.entity.v1.tripadvisor.common.silo_pb2 import Silo
from marketing_pb.entity.v1.tripadvisor.enums.silo_type_pb2 import SiloType

from common.config import Config
from src.utils.publisher import write_publisher_binary_data
from trip_advisor.mapping import BRAND_MAP, PNB_MAP

# Disable HTTP warnings globally
urllib3.disable_warnings(category=urllib3.exceptions.HTTPWarning)


def get_brand_config(brand):
    with open(f'src/trip_advisor/brand/{brand.upper()}.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config


class TAPartnerWorkflow:
    config: Config
    trino_con: ibis_trino.Backend
    duck_con: ibis_duck.Backend

    def __init__(self, config: Config, now_str=datetime.now().strftime("%Y-%m-%d-%H-%M-%S")):
        trino_auth = trino.auth.BasicAuthentication(
            username=config.sys_conf.trino.username,
            password=config.sys_conf.trino.password,
        )
        trino_api = trino.dbapi.connect(
            host=config.sys_conf.trino.host,
            port=config.sys_conf.trino.port,
            catalog=config.sys_conf.trino.catalog,
            http_scheme=config.sys_conf.trino.http_scheme,
            schema=config.sys_conf.trino.schema,
            verify=False,
            auth=trino_auth
        )
        trino_con = ibis_trino.Backend()
        trino_con.con = trino_api
        trino_con.connect()

        duck_con = ibis.duckdb.connect(config.sys_conf.duckdb.db_file)

        self.config = config
        self.trino_con = trino_con
        self.duck_con = duck_con

        self._base_bids_file = f'{self.config.sys_conf.bids_out}base_bids/{self.config.run_conf.brand.upper()}_hotels_partial_{now_str}.csv'
        self._audience_mult_file_path = f'{self.config.sys_conf.bids_out}audience_multipliers/{self.config.run_conf.brand.upper()}_audience_multipliers_{now_str}.csv'
        self._pnb_mult_file_path = f'{self.config.sys_conf.bids_out}pnb_multipliers/{self.config.run_conf.brand.upper()}_pnb_multipliers_{now_str}.csv'
        self._inv_file_path = f'{self.config.sys_conf.bids_out}inventory/tripadvisor_{self.config.run_conf.brand.lower()}_inventory_{now_str}.tsv'

        self.audience_missing = False
        self.pnb_missing = False

    def generate_bids(self):
        brand_config = get_brand_config(self.config.run_conf.brand.upper())
        self._load_inputs_bids()
        try:
            self._validate_input_bids()
        except Exception as e:
            loguru.logger.error(f'Validation failed {e}')
        self._fetch_and_load_inventory(brand_config)
        self._transform_bids(brand_config)
        self._write_bid_files()
        self._write_inventory_file()
        self._write_publisher_binary(brand_config)

    def _load_inputs_bids(self):
        loguru.logger.info('Loading input files into local DB...')
        input_files = [self.config.sys_conf.bids_local_path + i
                       for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                       if i.endswith('.csv.gz')]
        loguru.logger.info(f'Found {len(input_files)} files to be loaded')
        load_stmt = f"CREATE TABLE {self.config.sys_conf.duckdb.bids_working_table} as SELECT * from read_csv({input_files}, union_by_name=true)"
        if self.config.sys_conf.env != 'prod':
            loguru.logger.debug(load_stmt)
        self.duck_con.raw_sql(query=load_stmt)
        loguru.logger.info('Success!')

    def _validate_input_bids(self):
        loguru.logger.info('Processing validation rules on input bids...')
        bids_working = self.duck_con.table(self.config.sys_conf.duckdb.bids_working_table)

        # Submission types
        valid_submissions_types = ["BASE_BID", "PNB_MULTIPLIER", "AUDIENCE_MULTIPLIER"]
        invalid_sub_type = (~bids_working.select('submission_type')
                            .distinct()
                            .filter(bids_working['submission_type'].isin(valid_submissions_types))
                            .to_pandas()
                            .any()
                            ['submission_type']
                            )
        if invalid_sub_type:
            raise Exception('Bids with submission type not in "BASE_BID", "PNB_MULTIPLIER", "AUDIENCE_MULTIPLIER"')

        # Device types
        valid_device_types = ["DESKTOP", "MOBILE"]
        invalid_device_type = (~bids_working.select('device')
                               .distinct()
                               .filter(bids_working['device'].isin(valid_device_types))
                               .to_pandas()
                               .isin(valid_device_types)
                               .any()
                               ['device']
                               )
        if invalid_device_type:
            raise Exception('Bids with device type not in "DESKTOP", "MOBILE"')

        # Unique base_bid keys
        bids_working = bids_working.mutate(entity_key=bids_working['posa'].concat('.').concat(bids_working['hotel_id'].cast('string')).concat('.').concat(bids_working['device']))
        n_unique_keys = (bids_working.select('entity_key' ,'submission_type')
                         .filter(bids_working['submission_type'] == 'BASE_BID')
                         .nunique()
                         .to_pandas()
                        )
        n_keys = (bids_working.select('entity_key', 'submission_type')
                  .filter(bids_working['submission_type'] == 'BASE_BID')
                  .count()
                  .to_pandas()
                  )
        invalid_unique_key =  n_keys > n_unique_keys
        if invalid_unique_key:
            raise Exception('Base bids with non-unique posa.hotel_id.device key')

        # Base_bid empty hotel_ids
        base_bids_empty = (bids_working.select('hotel_id', 'submission_type')
                           .filter(bids_working['submission_type'] == 'BASE_BID')
                           ['hotel_id']
                           .isnull()
                           .to_pandas()
                           )
        invalid_empty_hotel_ids = base_bids_empty.any()
        if invalid_empty_hotel_ids:
            raise Exception('Base bids with empty hotel_id')

        # Value of bid_value_usd_cents for base_bid sub_type, PNB* should be 0-1300, otherwise 0
        PNB_vals = ['PNB', 'PNB2', 'PNB3', 'PNB4', 'PNB5', 'PNB6', 'PNB7', 'PNB8', 'PNB9', 'PNB10']
        bid_value_usd_cents_vals_valid = (bids_working.select('bid_value_usd_cents', 'hotel_group', 'submission_type')
                                          .filter((bids_working['submission_type'] == 'BASE_BID') & (bids_working['hotel_group'].isin(PNB_vals)))
                                          ['bid_value_usd_cents']
                                          .between(0, 1300)
                                          .count()
                                          .to_pandas()
                                          )
        bid_value_usd_cents_vals_total = (bids_working.select('bid_value_usd_cents', 'hotel_group', 'submission_type')
                                          .filter((bids_working['submission_type'] == 'BASE_BID') & (bids_working['hotel_group'].isin(PNB_vals)))
                                          ['bid_value_usd_cents']
                                          .count()
                                          .to_pandas()
                                          )
        invalid_bid_value_usd_cents = bid_value_usd_cents_vals_total > bid_value_usd_cents_vals_valid
        if invalid_bid_value_usd_cents:
            raise Exception('PNB bids with bid_value_usd_cents out of valid range')

        # Only null hotel group values for audience_bids
        audience_bids = (bids_working.select('hotel_group', 'submission_type')
                         .filter(bids_working['submission_type'] == 'AUDIENCE_MULTIPLIER')
                         ['hotel_group']
                         .isnull()
                         .to_pandas()
                         )
        invalid_audience_bids = ~audience_bids.all()
        if invalid_audience_bids:
            raise Exception('Audience bids with non null hotel_group')

        # Valid hotel group values pnb_bids
        pnb_valid_hotel_groups = ["PNB", "PNB2", "PNB3", "PNB4", "PNB5", "PNB6", "PNB7", "PNB8", "PNB9", "PNB10"]
        pnb_bids_valid = (bids_working.select('hotel_group', 'submission_type')
                          .filter(bids_working['submission_type'] == 'PNB_MULTIPLIER')
                          .filter(bids_working['hotel_group'].isin(pnb_valid_hotel_groups))
                          .count()
                          .to_pandas()
                          )
        pnb_bids_total = (bids_working.select('hotel_group', 'submission_type')
                          .filter(bids_working['submission_type'] == 'PNB_MULTIPLIER')
                          .filter(bids_working['hotel_group'].isin(pnb_valid_hotel_groups))
                          .count()
                          .to_pandas()
                          )
        invalid_pnb_bids = pnb_bids_total > pnb_bids_valid
        if invalid_pnb_bids:
            raise Exception('PNB bids with hotel_group not in "PNB", "PNB2", "PNB3", "PNB4", "PNB5", "PNB6", "PNB7", "PNB8", "PNB9", "PNB10"')

        # Valid hotel group values base_bids
        base_bids_valid_hotel_groups = ["Ignore", "CPA", "CPA1", "CPA2", "BMP", "BMP2", "BMP3", "BMP4", "BMP5", "PNB", "PNB2", "PNB3", "PNB4", "PNB5", "PNB6", "PNB7", "PNB8", "PNB9", "PNB10"]
        base_bids_valid = (bids_working.select('hotel_group', 'submission_type')
                           .filter(bids_working['submission_type'] == 'BASE_BID')
                           .filter(bids_working['hotel_group'].isin(base_bids_valid_hotel_groups))
                           ['hotel_group']
                           .count()
                           .to_pandas()
                           )
        base_bids_total = (bids_working.select('hotel_group', 'submission_type')
                           .filter(bids_working['submission_type'] == 'BASE_BID')
                           .filter(bids_working['hotel_group'].isin(base_bids_valid_hotel_groups))
                           ['hotel_group']
                           .count()
                           .to_pandas()
                           )
        invalid_base_bids = base_bids_total > base_bids_valid
        if invalid_base_bids:
            raise Exception('Base bids with hotel_group not in "Ignore", "CPA", "CPA1", "CPA2", "BMP", "BMP2", "BMP3", "BMP4", "BMP5", "PNB", "PNB2", "PNB3", "PNB4", "PNB5", "PNB6", "PNB7", "PNB8", "PNB9", "PNB10"')

        # Audience_multiplier non empty audience_id
        audience_bids_empty = (bids_working.select('audience_id', 'submission_type')
                               .filter(bids_working['submission_type'] == 'AUDIENCE_MULTIPLIER')
                               ['audience_id']
                               .isnull()
                               .to_pandas()
                               )
        invalid_audience_bids = audience_bids_empty.any()
        if invalid_audience_bids:
            raise Exception('Audience bids with null audience_id')

        # Pnb valid multiplier_type
        valid_pnb_mult = ["DEFAULT_DATE", "LENGTH_OF_STAY", "BOOKING_WINDOW", "GUESTS", "TIME_OF_DAY", "CHECK_IN_DAY_OF_WEEK", "CHECK_IN_DATE"]
        pnb_bids_valid = (bids_working.select('multiplier_type', 'submission_type')
                          .filter(bids_working['submission_type'] == 'PNB_MULTIPLIER')
                          .filter(bids_working['multiplier_type'].isin(valid_pnb_mult))
                          ['multiplier_type']
                          .count()
                          .to_pandas()
                          )

        pnb_bids_total = (bids_working.select('multiplier_type', 'submission_type')
                          .filter(bids_working['submission_type'] == 'PNB_MULTIPLIER')
                          ['multiplier_type']
                          .count()
                          .to_pandas()
                          )
        invalid_pnb_bids = pnb_bids_total > pnb_bids_valid
        if invalid_pnb_bids:
            raise Exception('PNB bids with multiplier_type not in "DEFAULT_DATE", "LENGTH_OF_STAY", "BOOKING_WINDOW", "GUESTS", "TIME_OF_DAY", "CHECK_IN_DAY_OF_WEEK", "CHECK_IN_DATE"')

        # Multiplier lower valid ranges
        # LENGTH_OF_STAY -> positive integer
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                     .filter(bids_working['multiplier_type'] == 'LENGTH_OF_STAY')
                     .filter(bids_working['multiplier_lower'].cast('float') > 0)
                     ['multiplier_lower']
                     .count()
                     .to_pandas()
                     )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'LENGTH_OF_STAY')
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('LENGTH_OF_STAY bids with multiplier_lower non positive')

        # DEFAULT_DATE -> 1
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'DEFAULT_DATE')
                      .filter(bids_working['multiplier_lower'].cast('float') == 1)
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'DEFAULT_DATE')
                      .filter(bids_working['multiplier_lower'].cast('float') == 1)
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('DEFAULT_DATE bids with multiplier_lower not 1')

        # BOOKING_WINDOW -> range 0,390
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'BOOKING_WINDOW')
                      .filter(bids_working['multiplier_lower'].cast('float').between(0, 390))
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'BOOKING_WINDOW')
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('BOOKING_WINDOW bids with multiplier_lower not between 0, 390')

        # GUESTS -> range 1,52
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'GUESTS')
                      .filter(bids_working['multiplier_lower'].cast('float').between(1, 52))
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter(bids_working['multiplier_type'] == 'GUESTS')
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('GUESTS bids with multiplier_lower not between 1, 52')

        # TIME_OF_DAY -> one of 0,6,12,18
        valid_mults = [0, 6, 12, 18]
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                .filter((bids_working['multiplier_type'] == 'TIME_OF_DAY'))
                .filter(bids_working['multiplier_lower'].cast('float').isin(valid_mults))
                ['multiplier_lower']
                .count()
                .to_pandas()
                )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'TIME_OF_DAY'))
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('TIME_OF_DAY bids with multiplier_lower not in 0, 6, 12, 18')

        # CHECK_IN_DAY_OF_WEEK -> range 1,7
        bids_valid = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'CHECK_IN_DAY_OF_WEEK'))
                      .filter((bids_working['multiplier_lower'].cast('float').between(1,7)))
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_lower', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'CHECK_IN_DAY_OF_WEEK'))
                      ['multiplier_lower']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('CHECK_IN_DAY_OF_WEEK bids with multiplier_lower not between 1,7')

        # Multiplier upper valid ranges
        # LENGTH_OF_STAY -> positive integer
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'LENGTH_OF_STAY'))
                      .filter((bids_working['multiplier_upper'].cast('float') > 0))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'LENGTH_OF_STAY'))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('LENGTH_OF_STAY bids with multiplier_upper not positive integer')

        # DEFAULT_DATE -> 1
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'DEFAULT_DATE'))
                      .filter((bids_working['multiplier_upper'].cast('float') == 1))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'DEFAULT_DATE'))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('DEFAULT_DATE bids with multiplier_upper not 1')

        # BOOKING_WINDOW -> range 0,390
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                .filter((bids_working['multiplier_type'] == 'BOOKING_WINDOW'))
                .filter((bids_working['multiplier_upper'].cast('float').between(0, 390)))
                ['multiplier_upper']
                .count()
                .to_pandas()
                )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'BOOKING_WINDOW'))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('BOOKING_WINDOW bids with multiplier_upper not between 0, 390')

        # GUESTS -> range 1,52
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'GUESTS'))
                      .filter((bids_working['multiplier_upper'].cast('float').between(1, 52)))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'GUESTS'))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('GUESTS bids with multiplier_upper not between 1, 52')

        # TIME_OF_DAY -> one of 5,11,17,23
        valid_mults = [5, 11, 17, 23]
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'TIME_OF_DAY'))
                      .filter((bids_working['multiplier_upper'].cast('float').isin(valid_mults)))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'TIME_OF_DAY'))
                      .filter((bids_working['multiplier_upper'].cast('float').isin(valid_mults)))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('TIME_OF_DAY bids with multiplier_upper not in 5, 11, 17, 23')

        # CHECK_IN_DAY_OF_WEEK -> range 1,7
        bids_valid = (bids_working.select('multiplier_upper', 'multiplier_type')
                     .filter((bids_working['multiplier_type'] == 'CHECK_IN_DAY_OF_WEEK'))
                     .filter((bids_working['multiplier_upper'].cast('float').between(1,7)))
                     ['multiplier_upper']
                     .count()
                     .to_pandas()
                     )
        bids_total = (bids_working.select('multiplier_upper', 'multiplier_type')
                      .filter((bids_working['multiplier_type'] == 'CHECK_IN_DAY_OF_WEEK'))
                      ['multiplier_upper']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('CHECK_IN_DAY_OF_WEEK bids with multiplier_upper not between 1,7')

        # Multiplier value valid ranges
        # sub_type AUDIENCE_MULTIPLIER -> range 0.001,10.00
        bids_valid = (bids_working.select('multiplier_value', 'submission_type')
                      .filter((bids_working['submission_type'] == 'AUDIENCE_MULTIPLIER'))
                      .filter((bids_working['multiplier_value'].cast('float').between(0.001,10.00)))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_value', 'submission_type')
                      .filter((bids_working['submission_type'] == 'AUDIENCE_MULTIPLIER'))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('AUDIENCE_MULTIPLIER bids with multiplier_value not between 0.001,10.00')

        # sub_type PNB_MULTIPLIER -> depends on mult type, default date 0.600,1, else 0.001,10.00
        mult_types = ["LENGTH_OF_STAY", "BOOKING_WINDOW", "GUESTS", "TIME_OF_DAY", "CHECK_IN_DAY_OF_WEEK", "CHECK_IN_DATE"]
        bids_valid = (bids_working.select('multiplier_value', 'submission_type', 'multiplier_type')
                      .filter((bids_working['submission_type'] == 'PNB_MULTIPLIER') & (bids_working['multiplier_type'].isin(mult_types)))
                      .filter((bids_working['multiplier_value'].cast('float').between(0.001,10.00)))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_value', 'submission_type', 'multiplier_type')
                      .filter((bids_working['submission_type'] == 'PNB_MULTIPLIER') & (bids_working['multiplier_type'].isin(mult_types)))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('PNB_MULTIPLIER bids with multiplier_value not between 0.001,10.00 for "LENGTH_OF_STAY", "BOOKING_WINDOW", "GUESTS", "TIME_OF_DAY", "CHECK_IN_DAY_OF_WEEK", "CHECK_IN_DATE"')

        # sub_type PNB_MULTIPLIER -> depends on mult type, default date 0.600,1, else 0.001,10.00
        bids_valid = (bids_working.select('multiplier_value', 'submission_type', 'multiplier_type')
                      .filter((bids_working['submission_type'] == 'PNB_MULTIPLIER') & (bids_working['submission_type'] == 'DEFAULT_DATE'))
                      .filter((bids_working['multiplier_value'].cast('float').between(0.600,1)))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        bids_total = (bids_working.select('multiplier_value', 'submission_type', 'multiplier_type')
                      .filter((bids_working['submission_type'] == 'PNB_MULTIPLIER') & (bids_working['submission_type'] == 'DEFAULT_DATE'))
                      ['multiplier_value']
                      .count()
                      .to_pandas()
                      )
        invalid_bids = bids_total > bids_valid
        if invalid_bids:
            raise Exception('PNB_MULTIPLIER bids with multiplier_value not between 0.600,1 for "DEFAULT_DATE"')
        loguru.logger.info('Success!')

    def _fetch_and_load_inventory(self, brand_config: {}):
        loguru.logger.info('Querying for latest inventory data from upstream tables...')
        brand_mapped = BRAND_MAP.get(self.config.run_conf.brand.upper())
        property_id_col = brand_config['input']['inventory']['propertyIdColumn']

        vulcan_table = self.trino_con.table(self.config.sys_conf.vulcan_update_table.split('.')[1],
                                            database=self.config.sys_conf.vulcan_update_table.split('.')[0])
        egumi_table = self.trino_con.table(self.config.sys_conf.egumi_table.split('.')[1],
                                           database=self.config.sys_conf.egumi_table.split('.')[0])

        max_date = vulcan_table.filter(vulcan_table.table_name == 'EGUMI').limit(1).select('latest_update_datetime_utc').to_pandas()
        biddable_condition = (egumi_table.update_datetime_utc == max_date.iloc[0, 0]) & (egumi_table['biddable']['tripadvisor'][brand_mapped]['status'] == True)
        biddable_ta_hotel_ids = egumi_table.filter(biddable_condition).select(hotel_id_vulcan=property_id_col)
        self.duck_con.create_table(self.config.sys_conf.duckdb.vulcan_ids, obj=biddable_ta_hotel_ids.execute())
        inventory_data = (egumi_table.filter(biddable_condition)
                          .select(hcom_property_id=egumi_table['hcom_property_id'].cast('string'),
                                  expedia_property_id=egumi_table['eg_property_id'].cast('string'),
                                  eg_property_id=egumi_table['eg_property_id'].cast('string'),
                                  name=egumi_table['property_name'],
                                  address1=egumi_table['first_address_line'],
                                  address2=egumi_table['second_address_line'],
                                  city=egumi_table['city_name'],
                                  state_province=egumi_table['state_province_name'],
                                  postal_code=egumi_table['postal_code'],
                                  country=egumi_table['country_code'].cast('string'),
                                  phone_number=egumi_table['phone_number'].cast('string'),
                                  longitude=egumi_table['longitude'],
                                  latitude=egumi_table['latitude'],
                                  rm_cnt=egumi_table['unit_count'].cast('string'),
                                  trip_custom_id=egumi_table['partner_id']['tripadvisor'].cast('string'))
                          )
        self.duck_con.create_table(self.config.sys_conf.duckdb.inventory_final, obj=inventory_data.execute())
        loguru.logger.info('Success!')

    def _transform_bids(self, brand_config: {}):
        loguru.logger.info('Transforming bids into TA format...')

        silos = brand_config['output']['columns']['siloList']
        bids_table = self.config.sys_conf.duckdb.bids_working_table

        # Create a silo map table for join against bids
        silo_table = 'silos_map'
        silos_map = [{'internal': silo['internalSiloName'], 'external': silo['tripAdvisorSiloName']} for silo in silos]
        self.duck_con.create_table(silo_table, obj=silos_map)

        # Base bids
        # Map internal silo names to external using a join and filter out silos not found in brand map
        bids_working = self.duck_con.table(bids_table)
        bids_working = (bids_working.sql(f"""
                                SELECT
                                    hotel_id AS hotelId,
                                    hotel_group AS hotelGroup,
                                    bid_value_usd_cents as bidBucket,
                                    {silo_table}.external AS silo
                                FROM
                                    {bids_table}
                                LEFT JOIN
                                    {silo_table}
                                ON
                                    {silo_table}.internal = CONCAT({bids_table}.posa, '-', {bids_table}.device)
                                WHERE
                                    {bids_table}.submission_type == 'BASE_BID'
                                    AND silo IS NOT NULL
                            """)
                        )

        # Inject placeholders for all silo's not found in bids
        silo_placeholder_rows = [{'hotelId': -1, 'silo': silo['tripAdvisorSiloName'], 'bidBucket': 0, 'hotelGroup': ''} for silo in silos]
        silo_placeholder_rows_table = self.duck_con.create_table('placeholder_silos_base', obj=silo_placeholder_rows)
        bids_working = bids_working.union(silo_placeholder_rows_table)
        siloed_base_bids_table = 'siloed_base_bids_table'
        self.duck_con.create_table(siloed_base_bids_table, obj=bids_working)

        # Pivot the table on the silos
        pivoted_base_bids_table = 'pivoted_base_bids_table'
        self.duck_con.raw_sql(f"""
                                    CREATE TABLE {pivoted_base_bids_table} AS
                                    PIVOT {siloed_base_bids_table}
                                    ON silo
                                    USING first(bidBucket) as BID, first(hotelGroup) as BIN
                                    GROUP BY hotelId
                                """)
        pivoted_bids = self.duck_con.table(pivoted_base_bids_table)

        # Remove placeholder silo rows and change hotelId to config name
        pivoted_bids = pivoted_bids.sql(f"""
                SELECT
                    hotelId AS '{brand_config['output']['columns']['baseBids']['hotelId']}',
                    *
                    FROM {pivoted_bids.get_name()}
            """)
        pivoted_bids = (pivoted_bids.filter(pivoted_bids['hotelId'].cast('int') > 0)
                        .select(pivoted_bids)
                        .order_by(pivoted_bids['hotelId'])
                        .drop(pivoted_bids['hotelId']))

        # Save base bids to final table
        self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids", obj=pivoted_bids)

        # Audience multipliers
        # Check if audience multipliers exist
        bids_working = self.duck_con.table(bids_table)
        if ((bids_working.select('submission_type')
                .filter(bids_working['submission_type'] == 'AUDIENCE_MULTIPLIER')
                .count()
                .to_pandas()
            ) > 0):
            # Map internal silo names to external using a join and filter out silos not found in brand map
            bids_working = (bids_working.sql(f"""
                                SELECT
                                    audience_id AS audienceId,
                                    multiplier_value AS multiplier,
                                    {silo_table}.external AS silo
                                FROM 
                                    {bids_table}
                                LEFT JOIN
                                    {silo_table}
                                ON
                                    {silo_table}.internal = CONCAT({bids_table}.posa, '-', {bids_table}.device)
                                WHERE
                                    {bids_table}.submission_type == 'AUDIENCE_MULTIPLIER'
                                    AND silo IS NOT NULL
                            """)
                            )

            # Inject placeholders for all silo's not found in bids
            silo_placeholder_rows = [{'audienceId': -1, 'silo': silo['tripAdvisorSiloName'], 'multiplier': 0.0} for silo in silos]
            silo_placeholder_rows_table = self.duck_con.create_table('placeholder_silos_audience', obj=silo_placeholder_rows)
            bids_working = bids_working.union(silo_placeholder_rows_table)
            siloed_audience_bids_table = 'siloed_audience_bids_table'
            self.duck_con.create_table(siloed_audience_bids_table, obj=bids_working)

            # Pivot the table on the silos
            pivoted_audience_bids_table = 'pivoted_audience_bids_table'
            self.duck_con.raw_sql(f"""
                                    CREATE TABLE {pivoted_audience_bids_table} AS
                                    PIVOT {siloed_audience_bids_table}
                                    ON silo
                                    USING first(multiplier)
                                    GROUP BY audienceId
                                """)
            pivoted_bids = self.duck_con.table(pivoted_audience_bids_table)

            # Remove placeholder silo rows and change hotelId to config name
            pivoted_bids = pivoted_bids.sql(f"""
                SELECT
                    audienceId AS '{brand_config['output']['columns']['audienceMultipliers']['audienceId']}',
                    *
                    FROM {pivoted_bids.get_name()}
            """)
            pivoted_bids = (pivoted_bids.filter(pivoted_bids['audienceId'].cast('int') > 0)
                            .select(pivoted_bids)
                            .order_by(pivoted_bids['audienceId'])
                            .drop(pivoted_bids['audienceId']))

            # Save base bids to final table
            self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_audience_multipliers", obj=pivoted_bids)
        else:
            loguru.logger.warning("Missing audience multipliers")
            self.audience_missing = True

        # PNB multipliers
        # Check if pnb multipliers exist
        bids_working = self.duck_con.table(bids_table)
        if ((bids_working.select('submission_type')
                .filter(bids_working['submission_type'] == 'PNB_MULTIPLIER')
                .count()
                .to_pandas()
            ) > 0):
            # Multiplier mapping
            pnb_mult_table = 'pnb_mult_table'
            pnb_mult_map = [{'internal': item[0], 'external': item[1]} for item in PNB_MAP.items()]
            self.duck_con.create_table(pnb_mult_table, obj=pnb_mult_map)

            # Map internal silo names to external using a join and filter out silos not found in brand map
            bids_working = self.duck_con.table(bids_table)
            bids_working = (bids_working.sql(f"""
                                SELECT
                                    {silo_table}.external AS silo,
                                    hotel_group AS bucket,
                                    {pnb_mult_table}.external AS bidLever,
                                    multiplier_lower AS rangeStart,
                                    multiplier_upper as rangeEnd,
                                    multiplier_value as multiplier,
                                    multiplier_type
                                FROM
                                    {bids_table}
                                LEFT JOIN
                                    {silo_table}
                                ON
                                    {silo_table}.internal = CONCAT({bids_table}.posa, '-', {bids_table}.device)
                                LEFT JOIN
                                    {pnb_mult_table}
                                ON
                                    {pnb_mult_table}.internal = {bids_table}.multiplier_type
                                WHERE
                                    {bids_table}.submission_type == 'PNB_MULTIPLIER'
                                    AND silo IS NOT NULL
                            """)
                            )
            bids_working = bids_working.order_by(bids_working['silo']).drop(bids_working['multiplier_type'])
            # Save base bids to final table
            self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_pnb_multipliers", obj=bids_working)
        else:
            loguru.logger.warning("Missing pnb multipliers")
            self.pnb_missing = True

        loguru.logger.info('Success!')

    def _write_bid_files(self):
        loguru.logger.info('Writing bids to partner file format...')

        # Base bids
        base_bid_file_path = self._base_bids_file
        os.makedirs(Path(base_bid_file_path).parent, exist_ok=True)
        base_bids_out = f"{self.config.sys_conf.duckdb.bids_final_table}_base_bids"
        self.duck_con.raw_sql(f"COPY (SELECT * FROM {base_bids_out}) TO '{base_bid_file_path}' WITH (HEADER, DELIMITER ',')")

        # Audience Multipliers
        if not self.audience_missing:
            audience_mult_file_path = self._audience_mult_file_path
            os.makedirs(Path(audience_mult_file_path).parent, exist_ok=True)
            audience_mult_out = f"{self.config.sys_conf.duckdb.bids_final_table}_audience_multipliers"
            self.duck_con.raw_sql(f"COPY (SELECT * FROM {audience_mult_out}) TO '{audience_mult_file_path}' WITH (HEADER, DELIMITER ',')")

        # PNB Multipliers
        if not self.pnb_missing:
            pnb_mult_file_path = self._pnb_mult_file_path
            os.makedirs(Path(pnb_mult_file_path).parent, exist_ok=True)
            pnb_mult_out = f"{self.config.sys_conf.duckdb.bids_final_table}_pnb_multipliers"
            self.duck_con.raw_sql(f"COPY (SELECT * FROM {pnb_mult_out}) TO '{pnb_mult_file_path}' WITH (HEADER, DELIMITER ',')")
            loguru.logger.info('Success!')

    def _write_inventory_file(self):
        loguru.logger.info('Writing inventory to partner file format...')

        inv_file_path = self._inv_file_path
        os.makedirs(Path(inv_file_path).parent, exist_ok=True)
        cleaned_table_query = f"""
                                (SELECT
                                    regexp_replace(trim(hcom_property_id), '[\r\n\t]', '',  'g') AS hcom_property_id,
                                    regexp_replace(trim(expedia_property_id), '[\r\n\t]', '',  'g') AS expedia_property_id,
                                    regexp_replace(trim(eg_property_id), '[\r\n\t]', '',  'g') AS eg_property_id,
                                    regexp_replace(trim(name), '[\r\n\t]', '',  'g') AS name,
                                    regexp_replace(trim(address1), '[\r\n\t]', '',  'g') AS address1,
                                    regexp_replace(trim(address2), '[\r\n\t]', '',  'g') AS address2,
                                    regexp_replace(trim(city), '[\r\n\t]', '',  'g') AS city,
                                    regexp_replace(trim(state_province), '[\r\n\t]', '',  'g') AS state_province,
                                    regexp_replace(trim(postal_code), '[\r\n\t]', '',  'g') AS postal_code,
                                    regexp_replace(trim(country), '[\r\n\t]', '',  'g') AS country,
                                    regexp_replace(trim(phone_number), '[\r\n\t]', '',  'g') AS phone_number,
                                    longitude,
                                    latitude,
                                    regexp_replace(trim(rm_cnt), '[\r\n\t]', '',  'g') AS rm_cnt,
                                    regexp_replace(trim(trip_custom_id), '[\r\n\t]', '',  'g') AS trip_custom_id
                                 FROM {self.config.sys_conf.duckdb.inventory_final})
                               """
        self.duck_con.raw_sql(f"COPY {cleaned_table_query} TO '{inv_file_path}' (FORMAT CSV, DELIMITER '\t', HEADER, QUOTE '', ESCAPE '')")
        loguru.logger.info('Success!')

    def _write_publisher_binary(self, brand_config: {}):
        # Hardcode account id for MP splitter bypass
        account_id = "1"
        silos = brand_config['output']['columns']['siloList']
        column_order_map = {silo['tripAdvisorSiloName']: silo['columnOrder'] for silo in silos}

        # Open bin file handle
        os.makedirs(os.path.dirname(self.config.sys_conf.bin_file), exist_ok=True)
        with open(self.config.sys_conf.bin_file, 'wb') as bin_out:
            # Base bids
            with open(self._base_bids_file, 'r') as f:
                reader = csv.DictReader(f)
                headers = reader.fieldnames
                # Order the headers according to column order map
                silo_headers = headers.copy()
                silo_headers.remove('Client ID')
                silos = set([val.split('_')[0] for val in silo_headers])

                for row in reader:
                    entity = MarketingEntity()
                    entity.account_id = account_id
                    entity.tripadvisor_entity.brand = self.config.run_conf.brand.upper()
                    entity.tripadvisor_entity.base_bid.client_id = row.get('Client ID')
                    for silo in silos:
                        column_order = column_order_map[silo]
                        silo_bid = Silo(trip_advisor_name=silo, value=row.get(f"{silo}_BID"), silo_type=SiloType.BID, column_order=column_order)
                        silo_bin = Silo(trip_advisor_name=silo, value=row.get(f"{silo}_BIN"), silo_type=SiloType.BIN, column_order=column_order)
                        entity.tripadvisor_entity.base_bid.silos.extend([silo_bid, silo_bin])
                    write_publisher_binary_data(bin_out, entity)

            # Audience mults
            if not self.audience_missing:
                with open(self._audience_mult_file_path, 'r') as f:
                    reader = csv.DictReader(f)
                    headers = reader.fieldnames
                    # Order the headers according to column order map
                    silo_headers = headers.copy()
                    silo_headers.remove('AUDIENCE ID')
                    silos = set([val.split('_')[0] for val in silo_headers])

                    for row in reader:
                        entity = MarketingEntity()
                        entity.account_id = account_id
                        entity.tripadvisor_entity.brand = self.config.run_conf.brand.upper()
                        entity.tripadvisor_entity.audience_multiplier.audience_id = row.get('AUDIENCE ID')
                        for silo in silos:
                            column_order = column_order_map[silo]
                            silo_bid = Silo(trip_advisor_name=silo, value=row.get(silo), silo_type=SiloType.BID, column_order=column_order)
                            entity.tripadvisor_entity.audience_multiplier.silos.extend([silo_bid])
                        write_publisher_binary_data(bin_out, entity)

            # PNB mults
            if not self.pnb_missing:
                with open(self._pnb_mult_file_path, 'r') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        entity = MarketingEntity()
                        entity.account_id = account_id
                        entity.tripadvisor_entity.brand = self.config.run_conf.brand.upper()
                        entity.tripadvisor_entity.pnb_multiplier.silo = row.get('silo')
                        entity.tripadvisor_entity.pnb_multiplier.bid_lever = row.get('bidLever')
                        entity.tripadvisor_entity.pnb_multiplier.range_start = row.get('rangeStart')
                        entity.tripadvisor_entity.pnb_multiplier.range_end = row.get('rangeEnd')
                        entity.tripadvisor_entity.pnb_multiplier.multiplier = row.get('multiplier')
                        entity.tripadvisor_entity.pnb_multiplier.bucket = row.get('bucket')
                        write_publisher_binary_data(bin_out, entity)

            # Inventory
            with open(self._inv_file_path, 'r') as f:
                reader = csv.DictReader(f, delimiter='\t', quoting=csv.QUOTE_NONE)
                for row in reader:
                    entity = MarketingEntity()
                    entity.account_id = account_id
                    entity.tripadvisor_entity.brand = self.config.run_conf.brand.upper()
                    entity.tripadvisor_entity.inventory.eg_property_id = row.get('eg_property_id')
                    entity.tripadvisor_entity.inventory.name = row.get('name')
                    entity.tripadvisor_entity.inventory.address_1 = row.get('address1')
                    entity.tripadvisor_entity.inventory.address_2 = row.get('address2')
                    entity.tripadvisor_entity.inventory.city = row.get('city')
                    entity.tripadvisor_entity.inventory.state_province = row.get('state_province')
                    entity.tripadvisor_entity.inventory.postal_code = row.get('postal_code')
                    entity.tripadvisor_entity.inventory.country = row.get('country')
                    entity.tripadvisor_entity.inventory.phone_number = row.get('phone_number')
                    entity.tripadvisor_entity.inventory.longitude = row.get('longitude')
                    entity.tripadvisor_entity.inventory.latitude = row.get('latitude')
                    entity.tripadvisor_entity.inventory.rm_cnt = row.get('rm_cnt')
                    entity.tripadvisor_entity.inventory.trip_custom_id = row.get('trip_custom_id')
                    entity.tripadvisor_entity.inventory.hcom_property_id = row.get('hcom_property_id')
                    entity.tripadvisor_entity.inventory.expedia_property_id = row.get('expedia_property_id')
                    write_publisher_binary_data(bin_out, entity)
