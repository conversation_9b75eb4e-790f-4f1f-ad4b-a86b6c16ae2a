from pyspark.sql.functions import lit, when, col, current_timestamp, to_timestamp
from pyspark.sql.types import StringType, DoubleType

def get_bid_history_mapping(brand, vulcan_run_id, timestamp):
    def _get_multiplier_level_end(multiplierLevel, multiplierType):
        return when(
            (multiplierType == 'LENGTH_OF_STAY') & (multiplierLevel == '4'), lit('7')
        ).when(
            (multiplierType == 'LENGTH_OF_STAY') & (multiplierLevel == '8'), lit('14')
        ).when(
            (multiplierType == 'LENGTH_OF_STAY') & (multiplierLevel == '15'), lit('15+')
        ).when(
            (multiplierType == 'DAYS_TO_ARRIVAL') & (multiplierLevel == '8'), lit('14')
        ).when(
            (multiplierType == 'DAYS_TO_ARRIVAL') & (multiplierLevel == '15'), lit('29')
        ).when(
            (multiplierType == 'DAYS_TO_ARRIVAL') & (multiplierLevel == '30'), lit('30+')
        ).otherwise(multiplierLevel.cast(StringType()))
    
    mapping = {
        "channel": lit('META'),
        "partner": lit("KAYAK"),
        "brand": lit(brand),
        "partner_submission_datetime_utc": timestamp,
        "partner_submission_id": lit(vulcan_run_id),
        "bsp_submission_datetime_utc": timestamp,
        "bsp_submission_id": lit(vulcan_run_id),
        "submission_type": col("submission_type"),
        "metadata": col("metadata"),
        "expected_rank": lit('NOT IN USE'),
        "posa": col("posa"),
        "device": col("device"),
        "placement": lit(None).cast(StringType()),
        "bid_status": lit(None).cast(StringType()),
        "experiment_name": col("experiment_name").cast(StringType()),
        "experiment_bucket": col("experiment_bucket").cast(StringType()),
        "bidding_level": lit(None).cast(StringType()),
        "bid_strategy_type": lit(None).cast(StringType()),
        "account_id": lit(None).cast(StringType()),
        "campaign_id": lit(None).cast(StringType()),
        "campaign_name": lit(None).cast(StringType()),
        "adgroup_name": lit(None).cast(StringType()),
        "hotel_id": col("hotel_id").cast(StringType()),
        "hotel_group": col("hotel_group"),
        "bid_value": col("bid_value").cast(DoubleType()),
        "multiplier_value": col("multiplier_value").cast(DoubleType()),
        "multiplier_type": col("multiplier_type"),
        "multiplier_level": col("multiplier_level"),
        "adgroup_id": lit(None).cast(StringType()),
        "account_name": lit(None).cast(StringType()),
        "keyword_id": lit(None).cast(StringType()),
        "multiplier_level_end": _get_multiplier_level_end(col("multiplier_level"), col("multiplier_type")),
        "audience_id": lit(None).cast(StringType()),
        "portfolio_id": lit(None).cast(StringType()),
        "portfolio_name": lit(None).cast(StringType()),
    }
    return mapping
