import glob
import os
import io
from datetime import datetime

import ibis
import loguru
import paramiko
import urllib3
import yaml
from ibis.backends import duckdb as ibis_duck

from common.config import Config, SFTPConfig


# Disable HTTP warnings globally
urllib3.disable_warnings(category=urllib3.exceptions.HTTPWarning)

class KayakPartnerWorkflow:
    config: Config
    duck_con: ibis_duck.Backend

    def __init__(self, config: Config):
        duck_con = ibis.duckdb.connect(config.sys_conf.duckdb.db_file)
        self.config = config
        self.duck_con = duck_con

    def generate_bids(self):
        now_str = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        self._load_inputs_bids()
        try:
            self._validate_input_bids()
        except Exception as e:
            loguru.logger.error(f'Validation failed {e}')
        self._transform_bids()
        self._write_bid_files(now_str)

    def _load_inputs_bids(self):
        loguru.logger.info('Loading input files into local DB...')
        input_files = [self.config.sys_conf.bids_local_path + i
                       for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                       if i.endswith('.csv.gz')]
        loguru.logger.info(f'Found {len(input_files)} files to be loaded')
        load_stmt = f"CREATE TABLE {self.config.sys_conf.duckdb.bids_working_table} as SELECT * from read_csv({input_files}, all_varchar=true)"
        if self.config.sys_conf.env != 'prod':
            loguru.logger.debug(load_stmt)
        print(f"input_files: {input_files}")
        self.duck_con.raw_sql(query=load_stmt)
        loguru.logger.info('Success!')

    def _validate_input_bids(self):
        loguru.logger.info('Processing validation rules on input bids...')
        bids_working = self.duck_con.table(self.config.sys_conf.duckdb.bids_working_table)

        # Submission types
        valid_submission_types = ["BASE_BID", "MULTIPLIER"]
        invalid_sub_type = (bids_working.select('submission_type')
                        .distinct()
                        .filter(~bids_working['submission_type'].isin(valid_submission_types))
                        .to_pandas()
                        .any()
                        ['submission_type']
                        )
        if invalid_sub_type:
            raise Exception('Bids with submission type not in "BASE_BID", "MULTIPLIER"')

        # Device types
        valid_device_types = ["DESKTOP", "MOBILE"]
        invalid_device_type = (bids_working.select('device')
                            .distinct()
                            .filter(~bids_working['device'].isin(valid_device_types))
                            .to_pandas()
                            .any()
                            ['device']
                            )
        if invalid_device_type:
            raise Exception('Bids with device type not in "DESKTOP", "MOBILE"')

        # POSA not empty
        invalid_posa = (bids_working.select('posa')
                        .filter(bids_working['posa'].isnull())
                        .to_pandas()
                        .any()
                        ['posa']
                        )
        if invalid_posa:
            raise Exception('Bids with empty posa')

        # Hotel ID should be positive integer for base bids
        invalid_hotel_id = (bids_working.select('hotel_id', 'submission_type')
                        .filter(bids_working['submission_type'] == 'BASE_BID')
                        .filter(~bids_working['hotel_id'].cast('int').between(1, float('inf')))
                        .to_pandas()
                        .any()
                        ['hotel_id']
                        )
        if invalid_hotel_id:
            raise Exception('Base bids with invalid hotel_id (must be a positive integer)')

        # Hotel ID should be empty for multipliers
        invalid_non_base_bid_hotel_id = (bids_working.select('hotel_id', 'submission_type')
                                        .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                        .filter(~bids_working['hotel_id'].isnull())
                                        .to_pandas()
                                        .any()
                                        ['hotel_id']
                                        )
        if invalid_non_base_bid_hotel_id:
            raise Exception('Multiplier bids with non-empty hotel_id (must be empty)')

        # Bid value should be positive for base bids
        invalid_bid_value = (bids_working.select('bid_value', 'submission_type')
                            .filter(bids_working['submission_type'] == 'BASE_BID')
                            .filter(~bids_working['bid_value'].cast('float').between(0, float('inf')))
                            .to_pandas()
                            .any()
                            ['bid_value']
                            )
        if invalid_bid_value:
            raise Exception('Base bids with invalid bid_value (must be within the range 0 to infinity)')

        # Bid value should be empty for mulitpliers
        invalid_non_base_bid_value = (bids_working.select('bid_value', 'submission_type')
                                    .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                    .filter(~bids_working['bid_value'].isnull())
                                    .to_pandas()
                                    .any()
                                    ['bid_value']
                                    )
        if invalid_non_base_bid_value:
            raise Exception('Multiplier bids with non-empty bid_value (must be empty)')

        # Hotel group not empty
        invalid_hotel_group = (bids_working.select('hotel_group')
                            .filter(bids_working['hotel_group'].isnull())
                            .to_pandas()
                            .any()
                            ['hotel_group']
                            )
        if invalid_hotel_group:
            raise Exception('Bids with empty hotel_group')

        # Brand not empty
        invalid_brand = (bids_working.select('brand')
                        .filter(bids_working['brand'].isnull())
                        .to_pandas()
                        .any()
                        ['brand']
                        )
        if invalid_brand:
            raise Exception('Bids with empty brand')

        # Check for invalid multiplier types for MULTIPLIER submission type
        valid_multiplier_types = ["DAYS_TO_ARRIVAL", "LENGTH_OF_STAY", "SATURDAY_NIGHT_STAY", "DISTANCE_TO_DESTINATION"]
        invalid_multiplier_type = (bids_working.select('multiplier_type', 'submission_type')
                                .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                .filter(~bids_working['multiplier_type'].isin(valid_multiplier_types))
                                .to_pandas()
                                .any()
                                ['multiplier_type']
                                )
        if invalid_multiplier_type:
            raise Exception('Multiplier bids with invalid multiplier_type')

        # Check non-empty multiplier types for non-MULTIPLIER submission type
        invalid_non_empty_multiplier_type = (bids_working.select('multiplier_type', 'submission_type')
                                    .filter(bids_working['submission_type'] != 'MULTIPLIER')
                                    .filter(~bids_working['multiplier_type'].isnull())
                                    .to_pandas()
                                    .any()
                                    ['multiplier_type']
                                    )
        if invalid_non_empty_multiplier_type:
            raise Exception('Non-MULTIPLIER bids with non-empty multiplier_type')

        # Multiplier level for MULTIPLIER
        valid_days_to_arrival = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "15", "30"]
        valid_length_of_stay = ["1", "2", "3", "4", "8", "15"]
        valid_saturday_night_stay = ["TRUE", "FALSE"]
        valid_distance_to_destination = ["SHORT", "MEDIUM", "LONG"]

        # Check for invalid multiplier levels for MULTIPLIER submission type
        invalid_multiplier_level = (bids_working.select('multiplier_level', 'multiplier_type', 'submission_type')
                                    .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                    .filter(
                                        ((bids_working['multiplier_type'] == 'DAYS_TO_ARRIVAL') & ~bids_working['multiplier_level'].isin(valid_days_to_arrival)) |
                                        ((bids_working['multiplier_type'] == 'LENGTH_OF_STAY') & ~bids_working['multiplier_level'].isin(valid_length_of_stay)) |
                                        ((bids_working['multiplier_type'] == 'SATURDAY_NIGHT_STAY') & ~bids_working['multiplier_level'].isin(valid_saturday_night_stay)) |
                                        ((bids_working['multiplier_type'] == 'DISTANCE_TO_DESTINATION') & ~bids_working['multiplier_level'].isin(valid_distance_to_destination))
                                    )
                                    .to_pandas()
                                    .any()
                                    ['multiplier_level']
                                    )
        if invalid_multiplier_level:
            print(invalid_multiplier_level)
            raise Exception('Multiplier bids with invalid multiplier_level')

        # Check for non-empty multiplier levels for non-MULTIPLIER submission type
        invalid_non_multiplier_level = (bids_working.select('multiplier_level', 'submission_type')
                                        .filter(bids_working['submission_type'] != 'MULTIPLIER')
                                        .filter(~bids_working['multiplier_level'].isnull())
                                        .to_pandas()
                                        .any()
                                        ['multiplier_level']
                                        )
        if invalid_non_multiplier_level:
            raise Exception('Non-multiplier bids with non-empty multiplier_level')

        # Multiplier value should be in range [0.01, 10] for MULTIPLIER 
        invalid_multiplier_value = (bids_working.select('multiplier_value', 'submission_type')
                                    .filter(bids_working['submission_type'] == 'MULTIPLIER')
                                    .filter(~bids_working['multiplier_value'].cast('float').between(0.01, 10))
                                    .to_pandas()
                                    .any()
                                    ['multiplier_value']
                                    )
        if invalid_multiplier_value:
            raise Exception('Multiplier bids with invalid multiplier_value (must be within the range 0.01 to 10)')

        # Multiplier value should be empty for non-MULTIPLIER
        invalid_non_multiplier_value = (bids_working.select('multiplier_value', 'submission_type')
                                        .filter(bids_working['submission_type'] != 'MULTIPLIER')
                                        .filter(~bids_working['multiplier_value'].isnull())
                                        .to_pandas()
                                        .any()
                                        ['multiplier_value']
                                        )
        if invalid_non_multiplier_value:
            raise Exception('Non-MULTIPLIER bids with non-empty multiplier_value')


        # Currency type for BASE_BID
        invalid_currency_type = (bids_working.select('currency_type', 'submission_type')
                                .filter(bids_working['submission_type'] == 'BASE_BID')
                                .filter(bids_working['currency_type'] != 'US_DOLLAR')
                                .to_pandas()
                                .any()
                                ['currency_type']
                                )
        if invalid_currency_type:
            raise Exception('Base bids with invalid currency_type (must be "US_DOLLAR")')

        # Currency type should be empty for non-BASE_BID
        invalid_non_base_bid_currency_type = (bids_working.select('currency_type', 'submission_type')
                                            .filter(bids_working['submission_type'] != 'BASE_BID')
                                            .filter(~bids_working['currency_type'].isnull())
                                            .to_pandas()
                                            .any()
                                            ['currency_type']
                                            )
        if invalid_non_base_bid_currency_type:
            raise Exception('Non-BASE_BID bids with non-empty currency_type')


        # Max CPC for BASE_BID
        invalid_max_cpc = (bids_working.select('max_cpc', 'submission_type')
                        .filter(bids_working['submission_type'] == 'BASE_BID')
                        .filter(~bids_working['max_cpc'].cast('float').between(0, float('inf')))
                        .to_pandas()
                        .any()
                        ['max_cpc']
                        )
        if invalid_max_cpc:
            raise Exception('Base bids with invalid max_cpc (must be within the range 0 to infinity)')

        # Max CPC should be empty for non-BASE_BID
        invalid_non_base_bid_max_cpc = (bids_working.select('max_cpc', 'submission_type')
                                        .filter(bids_working['submission_type'] != 'BASE_BID')
                                        .filter(~bids_working['max_cpc'].isnull())
                                        .to_pandas()
                                        .any()
                                        ['max_cpc']
                                        )
        if invalid_non_base_bid_max_cpc:
            raise Exception('Non-BASE_BID bids with non-empty max_cpc')

        loguru.logger.info('Success!')

    def _transform_bids(self):
        loguru.logger.info('Transforming full bids into Kayak format...')

        def _rename_multiplier_header_values(bids):
            los = "los"
            dta = "days_to_arrival"
            sns = "saturday_stay"
            dtd = "distance_to_destination"

            return bids.mutate(
                multiplier_type=ibis.case()
                    .when(bids.multiplier_type == 'LENGTH_OF_STAY', los)
                    .when(bids.multiplier_type == 'DAYS_TO_ARRIVAL', dta)
                    .when(bids.multiplier_type == 'SATURDAY_NIGHT_STAY', sns)
                    .when(bids.multiplier_type == 'DISTANCE_TO_DESTINATION', dtd)
                    .else_(bids.multiplier_type)
                    .end(),
                multiplier_level=bids.multiplier_level.lower()
            )

        def _verify_columns(bids):
            expected_columns = [
                "los_1", "los_2", "los_3", 
                "los_4", "los_8", "los_15", "days_to_arrival_0", "days_to_arrival_1", 
                "days_to_arrival_2", "days_to_arrival_3", "days_to_arrival_4", 
                "days_to_arrival_5", "days_to_arrival_6", "days_to_arrival_7", 
                "days_to_arrival_8", "days_to_arrival_15", "days_to_arrival_30", 
                "saturday_stay_true", "distance_to_destination_short", 
                "distance_to_destination_medium", "distance_to_destination_long"
            ]
            
            actual_columns = bids.select(bids.multiplier_header).distinct().execute()
            
            missing_columns = set(expected_columns) - set(actual_columns['multiplier_header'])
            
            if missing_columns:
                loguru.logger.error(f"Missing columns: {missing_columns}")
                print(actual_columns)
                raise Exception(f"Missing multiplier columns, must submit full set of bids. Missing columns: {', '.join(sorted(missing_columns))}")
        
        def _merge_base_bids_and_multipliers(base_bids, multipliers):
            all_bids = base_bids.join(
                multipliers, 
                (base_bids.hotel_group == multipliers.hotel_group) & 
                (base_bids.device == multipliers.device) & 
                (base_bids.posa == multipliers.posa)
            )[[
                base_bids.submission_type,
                base_bids.posa,
                base_bids.device,
                base_bids.hotel_id,
                base_bids.bid_value,
                multipliers.hotel_group,
                base_bids.brand,
                multipliers.multiplier_type,
                multipliers.multiplier_level,
                multipliers.multiplier_value,
                base_bids.currency_type,
                base_bids.max_cpc,
                base_bids.metadata,
                base_bids.experiment_name,
                base_bids.experiment_bucket
            ]]

            if all_bids.count().execute() == 0:
                raise Exception("Cannot join base bids and multipliers due to no common hotel_groups")
            
            # Cast columns to correct types
            all_bids = all_bids.mutate(
                submission_type=all_bids.submission_type.cast('string'),
                posa=all_bids.posa.cast('string'),
                device=all_bids.device.cast('string'),
                hotel_id=all_bids.hotel_id.cast('int64'),
                bid_value=all_bids.bid_value.cast('float64'),
                hotel_group=all_bids.hotel_group.cast('string'),
                brand=all_bids.brand.cast('string'),
                multiplier_type=all_bids.multiplier_type.cast('string'),
                multiplier_level=all_bids.multiplier_level.cast('string'),
                multiplier_value=all_bids.multiplier_value.cast('float64'),
                currency_type=all_bids.currency_type.cast('string'),
                max_cpc=all_bids.max_cpc.cast('float64'),
                metadata=all_bids.metadata.cast('string'),
                experiment_name=all_bids.experiment_name.cast('string'),
                experiment_bucket=all_bids.experiment_bucket.cast('string')
            )
            
            return all_bids


        pivot_values = [
            "los_1", "los_2", "los_3", "los_4", "los_8", "los_15", 
            "days_to_arrival_0", "days_to_arrival_1", "days_to_arrival_2", 
            "days_to_arrival_3", "days_to_arrival_4", "days_to_arrival_5", 
            "days_to_arrival_6", "days_to_arrival_7", "days_to_arrival_8", 
            "days_to_arrival_15", "days_to_arrival_30", "saturday_stay_true", 
            "distance_to_destination_short", "distance_to_destination_medium", 
            "distance_to_destination_long"
        ]
        
        bids_table = self.config.sys_conf.duckdb.bids_working_table

        # Rename multiplier header values
        bids_working = self.duck_con.table(bids_table)
        base_bids = bids_working.filter(bids_working.submission_type == 'BASE_BID')
        multipliers = bids_working.filter(bids_working.submission_type == 'MULTIPLIER')

        # Desktop bids
        # Filter out desktop multipliers
        desktop_multipliers = multipliers.filter(multipliers.device == "DESKTOP")

        if desktop_multipliers.count().execute() > 0:

            # Filter out desktop base bids
            desktop_base_bids = base_bids.filter(base_bids.device == "DESKTOP")
            
            # Merge base bids and multipliers
            desktop_bids_working = _merge_base_bids_and_multipliers(desktop_base_bids, desktop_multipliers)

            # Rename multiplier header values
            desktop_bids_working = _rename_multiplier_header_values(desktop_bids_working)
            
            # Create a new column for the multiplier header
            desktop_bids_working = desktop_bids_working.mutate(
                multiplier_header=ibis.literal('_').join([desktop_bids_working.multiplier_type, desktop_bids_working.multiplier_level])
            )

            # Verify that all columns are present
            _verify_columns(desktop_bids_working)

            # Pivot the table
            pivoted_desktop_bids_table = 'pivoted_desktop_bids_table'
            pivoted_table = desktop_bids_working.pivot_wider(
                id_cols=['hotel_id', 'bid_value', 'max_cpc', 'hotel_group'],
                names_from='multiplier_header',
                values_from='multiplier_value'
            )
            pivoted_desktop_bids = self.duck_con.create_table(pivoted_desktop_bids_table, obj=pivoted_table)

            # Rename columns and select specific columns for output
            pivoted_desktop_bids = pivoted_desktop_bids.sql(f"""
                SELECT
                    '' AS 'kayak_entity.hotel_bid.portfolio_name',
                    hotel_id AS 'kayak_entity.hotel_bid.hotel_id',
                    bid_value AS 'kayak_entity.hotel_bid.base_bob_cpc',
                    {', '.join([f'{col} AS "kayak_entity.hotel_bid.{col}"' for col in pivot_values])},
                    max_cpc AS 'kayak_entity.hotel_bid.max_cpc',
                    'FULL_SITE' AS 'kayak_entity.platform',
                    '1' AS 'account_id',
                FROM {pivoted_desktop_bids_table}
                ORDER BY CAST(hotel_id AS STRING)
            """)

            # Save the transformed desktop bids
            self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_desktop_bids", obj=pivoted_desktop_bids)

        # Mobile bids
        # Filter out mobile multipliers
        mobile_multipliers = multipliers.filter(multipliers.device == "MOBILE")

        if mobile_multipliers.count().execute() > 0:
            # Filter out mobile base bids
            mobile_base_bids = base_bids.filter(base_bids.device == "MOBILE")
            
            # Merge base bids and multipliers
            mobile_bids_working = _merge_base_bids_and_multipliers(mobile_base_bids, mobile_multipliers)

            # Rename multiplier header values
            mobile_bids_working = _rename_multiplier_header_values(mobile_bids_working)
            
            # Create a new column for the multiplier header
            mobile_bids_working = mobile_bids_working.mutate(
                multiplier_header=ibis.literal('_').join([mobile_bids_working.multiplier_type, mobile_bids_working.multiplier_level])
            )

            # Verify that all columns are present
            _verify_columns(mobile_bids_working)
            
            # Pivot the table
            pivoted_mobile_bids_table = 'pivoted_mobile_bids_table'
            pivoted_table = mobile_bids_working.pivot_wider(
                id_cols=['hotel_id', 'bid_value', 'max_cpc', 'hotel_group'],
                names_from='multiplier_header',
                values_from='multiplier_value'
            )
            pivoted_mobile_bids = self.duck_con.create_table(pivoted_mobile_bids_table, obj=pivoted_table)

            # Rename columns and select specific columns for output
            pivoted_mobile_bids = pivoted_mobile_bids.sql(f"""
                SELECT
                    '' AS 'kayak_entity.hotel_bid.portfolio_name',
                    hotel_id AS 'kayak_entity.hotel_bid.hotel_id',
                    bid_value AS 'kayak_entity.hotel_bid.base_bob_cpc',
                    {', '.join([f'{col} AS "kayak_entity.hotel_bid.{col}"' for col in pivot_values])},
                    max_cpc AS 'kayak_entity.hotel_bid.max_cpc',
                    'MOBILE' AS 'kayak_entity.platform',
                    '1' AS 'account_id',
                FROM {pivoted_mobile_bids_table}
                ORDER BY CAST(hotel_id AS STRING)
            """)

            # Save the transformed mobile bids
            self.duck_con.create_table(f"{self.config.sys_conf.duckdb.bids_final_table}_mobile_bids", obj=pivoted_mobile_bids)

            loguru.logger.info('Success!')

    def _write_bid_files(self, now_str: str):
        loguru.logger.info('Writing bids to partner file format...')
        os.makedirs(self.config.sys_conf.bids_out, exist_ok=True)

        # Get posas
        bids_table = self.config.sys_conf.duckdb.bids_working_table
        bids_working = self.duck_con.table(bids_table)
        posa = bids_working.select('posa').distinct().execute().iloc[0]['posa']

        def _write_bid_file(file_path, query):
            self.duck_con.raw_sql(f"COPY ({query}) TO '{file_path}' WITH (HEADER, DELIMITER ',')")

        def _write_header_file(file_path):
            header = "kayak_entity.header.country,kayak_entity.header.mode,kayak_entity.header.hotel_brand,kayak_entity.header.optional_portfolio_id,kayak_entity.header.lob,kayak_entity.header.placement_type,account_id\n"
            with open(file_path, 'w') as f:
                f.write(header)
                f.write(f"{posa},Full,{self.config.run_conf.brand.upper()},\"\",LOB_HOTELS,PLACEMENT_TYPE_UNSPECIFIED,1")

        # Desktop bids
        desktop_bids_out = f"{self.config.sys_conf.duckdb.bids_final_table}_desktop_bids"
        desktop_bids_file_path = f'{self.config.sys_conf.bids_out}Kayak_Hotel_BOB_{posa}_{self.config.run_conf.brand.upper()}_FULL_SITE_v2_{now_str}.csv'
        desktop_query = f"SELECT * FROM {desktop_bids_out}"
        _write_bid_file(desktop_bids_file_path, desktop_query)

        # Mobile bids
        mobile_bids_out = f"{self.config.sys_conf.duckdb.bids_final_table}_mobile_bids"
        mobile_bids_file_path = f'{self.config.sys_conf.bids_out}Kayak_Hotel_BOB_{posa}_{self.config.run_conf.brand.upper()}_MOBILE_v2_{now_str}.csv'
        mobile_query = f"SELECT * FROM {mobile_bids_out}"
        _write_bid_file(mobile_bids_file_path, mobile_query)

        # Header file
        header_file_path = f'{self.config.sys_conf.bids_out}Kayak_Hotel_BOB_{posa}_{self.config.run_conf.brand.upper()}_HEADER_{now_str}.csv'
        _write_header_file(header_file_path)

class NonCoreKayakPartnerWorkflow:
    config: Config
    input_files: list
    sftpConfig: SFTPConfig

    def __init__(self, config: Config, sftpConfig: SFTPConfig):
        self.config = config
        self.sftpConfig = sftpConfig

    def generate_bids(self):
        self._load_inputs_bids()
        self._upload_to_sftp()

    def _load_inputs_bids(self):
        loguru.logger.info('Loading inputs bids')
        self.input_files = [self.config.sys_conf.bids_local_path + i
                       for i in glob.iglob(f'**/*', root_dir=self.config.sys_conf.bids_local_path, recursive=True)
                       if i.endswith('.xlsx')]

    def _upload_to_sftp(self):
        loguru.logger.info('Uploading to SFTP')
        ssh_client = paramiko.SSHClient()

        host = os.environ.get('SFTP_HOST')
        username = os.environ.get('SFTP_USERNAME')
        private_key_content = os.environ.get('SFTP_PASSWORD')

        key = paramiko.RSAKey.from_private_key(io.StringIO(private_key_content))

        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(host, username=username, pkey=key)

        loguru.logger.info('connected to sftp')

        sftp = ssh_client.open_sftp()

        if os.environ.get('PUBLISH') == 'True':
            if len(self.input_files) == 0:
                raise FileNotFoundError("No Excel files found in the specified directory.")
            else:
                file = self.input_files[0]  # Directly access the single file
                filename = os.path.basename(file)
                remote_path = f'/search_term_imports/{filename}'
                sftp.put(file, remote_path)

        loguru.logger.info('uploaded to sftp successful')
        sftp.close()
        ssh_client.close()