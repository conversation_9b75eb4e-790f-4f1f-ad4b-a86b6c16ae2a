import os
from datetime import datetime

import loguru
import shutil
import pandas as pd
import csv

from common.config import Config

class KayakCarsPartnerWorkflow:
    config: Config

    def __init__(self, config: Config):
        self.config = config

    def generate_bids(self):
        now_str = datetime.now().strftime("%Y-%m-%d")
        self._build_header(now_str)
        self._rename_columns_in_all_csv()
        self._copy_files()

    def _build_header(self, now_str):

        # Read the first file encountered in the directory to get header info
        input_csv_data = None
        filename = os.listdir(self.config.sys_conf.bids_local_path)[0]
        input_csv_path = os.path.join(self.config.sys_conf.bids_local_path, filename)
        _, header_line_count = self._get_platform_and_header_line_count(input_csv_path)
        with open(input_csv_path, 'r') as f:
            input_csv_data = ''.join([f.readline() for _ in range(header_line_count)])

        if input_csv_data is None:
            raise FileNotFoundError("No CSV files found in the specified directory.")

        # Parse the input CSV data
        input_data = {}
        for line in input_csv_data.split('\n'):
            if ',' in line:
                key, value = line.strip().split(',', 1)
                input_data[key] = value

        # Transform the data into the desired format
        placement_type = "PLACEMENT_TYPE_CORE"
        if input_data.get("Whisky") == "Yes":
            placement_type = "PLACEMENT_TYPE_WHISKY"
        
        output_data = [
            ["kayak_entity.header.country", "kayak_entity.header.mode", "kayak_entity.header.hotel_brand", "kayak_entity.header.optional_portfolio_id", "kayak_entity.header.lob", "kayak_entity.header.placement_type", "account_id"],
            [input_data.get("Country", ""), input_data.get("Mode", ""), "", input_data.get("Optional Portfolio ID", ""), "LOB_CARS", placement_type, "1"]
        ]

        os.makedirs(self.config.sys_conf.bids_out, exist_ok=True)
        header_file_path = f'{self.config.sys_conf.bids_out}0-0-{self.config.run_conf.brand.upper()}_{input_data.get("Country", "")}_HEADER_{now_str}_kayak_cars_bob_bids.csv'

        # Write the transformed data to a new CSV file
        with open(header_file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(output_data)

        loguru.logger.info(f'Created header file: {header_file_path}')

    def _rename_columns_in_all_csv(self):
        column_mapping = self._get_column_mapping()
        for filename in os.listdir(self.config.sys_conf.bids_local_path):
            if filename.endswith('.csv'):
                input_csv = os.path.join(self.config.sys_conf.bids_local_path, filename)
                self._rename_csv_columns(input_csv, column_mapping)
                loguru.logger.info(f'Renamed columns in {input_csv}')

    def _copy_files(self):
        dest_folder = self.config.sys_conf.bids_out
        os.makedirs(dest_folder, exist_ok=True)
        src_folder = self.config.sys_conf.bids_local_path
        for filename in os.listdir(src_folder):
            if filename.endswith('.csv'):
                src_file = os.path.join(src_folder, filename)
                dest_file = os.path.join(dest_folder, filename)
                shutil.copy(src_file, dest_file)
                loguru.logger.info(f'Copied {src_file} to {dest_file}')

    def _rename_csv_columns(self, input_csv, column_mapping):
        platform, header_line_count = self._get_platform_and_header_line_count(input_csv)
        df = pd.read_csv(input_csv, skiprows=header_line_count)
        df.rename(columns=column_mapping, inplace=True)
        df['kayak_entity.platform'] = platform
        df['account_id'] = '1'
        df.to_csv(input_csv, index=False)
    
    def _get_platform_and_header_line_count(self, input_csv):
        platform = ""
        header_line_count = 0
        with open(input_csv, 'r') as f:
            line = ''
            while 'Portfolio Name' not in line:
                line = f.readline().strip()
                if 'Client Platform' in line:
                    platform = line.split(',')[1]
                header_line_count += 1

        return platform, header_line_count - 1

    def _get_column_mapping(self):
        return {
            'Portfolio Name': 'kayak_entity.car_bid.portfolio_name',
            'Location ID': 'kayak_entity.car_bid.location_id',
            'Agency ID': 'kayak_entity.car_bid.agency_id',
            'Base BOB CPC': 'kayak_entity.car_bid.base_bob_cpc',
            'Days to Arrival:0': 'kayak_entity.car_bid.days_to_arrival_0',
            'Days to Arrival:1': 'kayak_entity.car_bid.days_to_arrival_1',
            'Days to Arrival:15-29': 'kayak_entity.car_bid.days_to_arrival_15',
            'Days to Arrival:2': 'kayak_entity.car_bid.days_to_arrival_2',
            'Days to Arrival:3': 'kayak_entity.car_bid.days_to_arrival_3',
            'Days to Arrival:30+': 'kayak_entity.car_bid.days_to_arrival_30',
            'Days to Arrival:4': 'kayak_entity.car_bid.days_to_arrival_4',
            'Days to Arrival:5': 'kayak_entity.car_bid.days_to_arrival_5',
            'Days to Arrival:6': 'kayak_entity.car_bid.days_to_arrival_6',
            'Days to Arrival:7': 'kayak_entity.car_bid.days_to_arrival_7',
            'Days to Arrival:8-14': 'kayak_entity.car_bid.days_to_arrival_8',
            'Car Class:*E**': 'kayak_entity.car_bid.car_class_e',
            'Car Class:*F**': 'kayak_entity.car_bid.car_class_f',
            'Car Class:*J**': 'kayak_entity.car_bid.car_class_j',
            'Car Class:*K**': 'kayak_entity.car_bid.car_class_k',
            'Car Class:*Q**': 'kayak_entity.car_bid.car_class_q',
            'Car Class:*T**': 'kayak_entity.car_bid.car_class_t',
            'Car Class:*V**': 'kayak_entity.car_bid.car_class_v',
            'Car Class:C***': 'kayak_entity.car_bid.car_class_c',
            'Car Class:CF**': 'kayak_entity.car_bid.car_class_cf',
            'Car Class:CW**': 'kayak_entity.car_bid.car_class_cw',
            'Car Class:E***': 'kayak_entity.car_bid.car_class_e_3',
            'Car Class:EW**': 'kayak_entity.car_bid.car_class_ew',
            'Car Class:F***': 'kayak_entity.car_bid.car_class_f_3',
            'Car Class:FF**': 'kayak_entity.car_bid.car_class_ff',
            'Car Class:FW**': 'kayak_entity.car_bid.car_class_fw',
            'Car Class:I***': 'kayak_entity.car_bid.car_class_i',
            'Car Class:IF**': 'kayak_entity.car_bid.car_class_if',
            'Car Class:IW**': 'kayak_entity.car_bid.car_class_iw',
            'Car Class:L***': 'kayak_entity.car_bid.car_class_l',
            'Car Class:LE**': 'kayak_entity.car_bid.car_class_le',
            'Car Class:LF**': 'kayak_entity.car_bid.car_class_lf',
            'Car Class:LW**': 'kayak_entity.car_bid.car_class_lw',
            'Car Class:M***': 'kayak_entity.car_bid.car_class_m',
            'Car Class:MV**': 'kayak_entity.car_bid.car_class_mv',
            'Car Class:O***': 'kayak_entity.car_bid.car_class_o',
            'Car Class:P***': 'kayak_entity.car_bid.car_class_p',
            'Car Class:PE**': 'kayak_entity.car_bid.car_class_pe',
            'Car Class:PF**': 'kayak_entity.car_bid.car_class_pf',
            'Car Class:PW**': 'kayak_entity.car_bid.car_class_pw',
            'Car Class:S***': 'kayak_entity.car_bid.car_class_s',
            'Car Class:SF**': 'kayak_entity.car_bid.car_class_sf',
            'Car Class:SW**': 'kayak_entity.car_bid.car_class_sw',
            'Car Class:X***': 'kayak_entity.car_bid.car_class_x',
            'Car Class:XC**': 'kayak_entity.car_bid.car_class_xc',
            'Car Class:XE**': 'kayak_entity.car_bid.car_class_xe',
            'Car Class:XF**': 'kayak_entity.car_bid.car_class_xf',
            'Car Class:XQ**': 'kayak_entity.car_bid.car_class_xq',
            'Car Class:XV**': 'kayak_entity.car_bid.car_class_xv',
            'Car Class:XW**': 'kayak_entity.car_bid.car_class_xw',
            'Trip Type:OW': 'kayak_entity.car_bid.trip_type_ow',
            'Trip Type:RT': 'kayak_entity.car_bid.trip_type_rt',
            'LOS:0': 'kayak_entity.car_bid.los_0',
            'LOS:1': 'kayak_entity.car_bid.los_1',
            'LOS:15-21': 'kayak_entity.car_bid.los_15',
            'LOS:2': 'kayak_entity.car_bid.los_2',
            'LOS:22-28': 'kayak_entity.car_bid.los_22',
            'LOS:29+': 'kayak_entity.car_bid.los_29',
            'LOS:3': 'kayak_entity.car_bid.los_3',
            'LOS:4': 'kayak_entity.car_bid.los_4',
            'LOS:5': 'kayak_entity.car_bid.los_5',
            'LOS:6': 'kayak_entity.car_bid.los_6',
            'LOS:7': 'kayak_entity.car_bid.los_7',
            'LOS:8-14': 'kayak_entity.car_bid.los_8',
            'Payment Type:Postpay': 'kayak_entity.car_bid.payment_type_postpay',
            'Payment Type:Prepay': 'kayak_entity.car_bid.payment_type_prepay',
            'Saturday Stay:False': 'kayak_entity.car_bid.saturday_stay_false',
            'Saturday Stay:True': 'kayak_entity.car_bid.saturday_stay_true',
            'Max CPC': 'kayak_entity.car_bid.max_cpc'
        }