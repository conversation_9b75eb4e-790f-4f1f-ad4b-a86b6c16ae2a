import subprocess

import tenacity


@tenacity.retry(
    wait=tenacity.wait_exponential(min=10, multiplier=1, max=30),
    stop=tenacity.stop_after_attempt(3),
    reraise=True,
)
def s3_sync(origin, destination):
    command = f'aws s3 sync {origin} {destination}'
    exit_code = subprocess.call(command.split())
    if exit_code:
        raise Exception


def s3_cp(origin, destination):
    command = f'aws s3 cp {origin} {destination}'
    exit_code = subprocess.call(command.split())
    if exit_code:
        raise Exception
