import os
import time

import requests
import tenacity
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


@tenacity.retry(
    wait=tenacity.wait_exponential(min=10, multiplier=1, max=30),
    stop=tenacity.stop_after_attempt(3),
    reraise=True,
    retry=tenacity.retry_if_exception_type(requests.exceptions.SSLError),
)
def start_dag(dag_id: str, conf: dict) -> str:
    start_dag_endpoint = f'{os.getenv("AIRFLOW_URL")}/{dag_id}/dagRuns'
    response = requests.post(
        url=start_dag_endpoint,
        json={'conf': conf},
        verify=False,
        auth=(os.getenv('AIRFLOW_USER'), os.getenv('AIRFLOW_PASSWORD')),
    )
    response.raise_for_status()
    return response.json().get('dag_run_id')


@tenacity.retry(
    wait=tenacity.wait_exponential(min=10, multiplier=1, max=30),
    stop=tenacity.stop_after_attempt(3),
    reraise=True,
    retry=tenacity.retry_if_exception_type(requests.exceptions.SSLError),
)
def monitor_dag_run(dag_id, run_id):
    status_url = f'{os.getenv("AIRFLOW_URL")}/{dag_id}/dagRuns/{run_id}'
    while True:
        response = requests.get(
            url=status_url,
            verify=False,
            auth=(os.getenv('AIRFLOW_USER'), os.getenv('AIRFLOW_PASSWORD')),
        )
        response.raise_for_status()
        dag_run_info = response.json()
        state = dag_run_info.get('state')
        print(f'DAG Run ID: {run_id}, State: {state}')

        if state in {'success', 'failed', 'up_for_retry', 'skipped'}:
            break
        time.sleep(60)  # Wait before checking again
