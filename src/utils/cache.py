import json
import os
from typing import Optional

import loguru
import redis


REDIS_HOST = os.environ.get('DRAGONFLY_URL', 'dragonfly') # 
REDIS_PORT = 6379

class Cache:
    redis_client: redis.Redis
    cache_key: str
    timeout: int

    def __init__(self, cache_key: str, timeout: Optional[int]):
        self.redis_client = _get_redis_client()
        self.cache_key = cache_key
        self.logger = loguru.logger
        if timeout:
            self.timeout = timeout
        else:
            # 12 hour cache
            self.timeout = 12 * 60 * 60  # 12 hours (in seconds)

    def get_cache(self) -> dict:
        data = self.redis_client.get(self.cache_key)
        try:
            return json.loads(data)
        except Exception as e:
            self.logger.info("Empty cache found in redis")
            return {}

    def store_cache(self, value: dict):
        self.redis_client.set(name=self.cache_key, value=json.dumps(value), ex=self.timeout)

    def update_cache(self, value: dict):
        self.redis_client.set(name=self.cache_key, value=json.dumps(value))


def _get_redis_client() -> redis.Redis:
    """
    Get a Redis client instance with the configured connection parameters.

    Returns:
        redis.Redis: A configured Redis client
    """
    return redis.Redis(host=REDIS_HOST, port=REDIS_PORT)
