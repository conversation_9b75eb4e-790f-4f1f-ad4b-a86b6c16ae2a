import argparse

from src import splitter, google_hotel_ads, bing_search

parser = argparse.ArgumentParser()
parser.add_argument(
    '--inputS3Path', type=str, help='input bids s3 path or file based on how your algo expects',
)
parser.add_argument(
    '--channel', type=str, help='marketing channel to run for',
)
parser.add_argument(
    '--runId', type=str, help='The unique id for the run',
)

args = parser.parse_args()
splitter.run(args.inputS3Path)

if args.channel == 'google-hotel-ads':
    google_hotel_ads.Generator.generate_reports_to_download(args.runId)
if args.channel == 'bing-search':
    bing_search.Generator.generate_reports_to_download(args.runId)
