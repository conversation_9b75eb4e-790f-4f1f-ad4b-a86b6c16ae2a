import os
from datetime import datetime, timezone
from typing import List

import loguru
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from utils.cache import Cache


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10), reraise=True)
def post_with_retry(url, json_data):
    response = requests.post(url, json=json_data)
    response.raise_for_status()
    return response

def get_config(run_labels: List[str]):
    return {
        'workflow_name': 'src.python.eg_meta_spend_controller.workflows.orchestrator.spend_controller_orchestrator',
        'project': 'eg-meta-helicon',
        'domain': 'prd',
        'config_path': 'config/production',
        'label': run_labels[0].split('-')[1].upper(),
        'task_date': datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    }

def run_spend_controller(run_labels: List[str]):
    run_label = run_labels[0]
    loguru.logger.info(f'Running Spend controller for run_label: {run_label}')
    if run_label in {'hcom-apac', 'expedia-apac','expedia-emea', 'hcom-emea', 'expedia-latam', 'hcom-latam','expedia-na', 'hcom-na' } and len(run_labels) == 2:
        if resolve_controller_trigger_state(run_label):
            resp = post_with_retry(f'{os.getenv("OTTOMAN_API")}/triggerSpendControllerWorkflow', get_config(run_labels))
            resp.raise_for_status()
            loguru.logger.info(f'Spend controller triggerred successfully.')

def resolve_controller_trigger_state(run_label) -> bool:
    region = run_label.split('-')[1]
    cache_key = f'gha-spend-controller-{region}'
    cache = Cache(cache_key, None)

    def _get_init_state(current_run):
        # All dependent run labels should be indicated here
        state = {'hcom': '', 'expedia': ''}
        state[current_run.split('-')[0]] = "finished"
        return state

    data = cache.get_cache()
    if not data:
        # First run, set state cache
        loguru.logger.info(f"No data in cache found for {region}, indicating first run.")
        init_state = _get_init_state(run_label)
        cache.store_cache(init_state)
        return False
    else:
        # Set run status
        loguru.logger.info(f'Updating cache for {region} with run status')
        data[run_label.split('-')[0]] = "finished"

    # If all runs finished
    trigger = True
    for val in data.values():
        if val != "finished":
            trigger = False

    if trigger:
        loguru.logger.info(f'All runs complete, spend controller valid to run')
        return True
    else:
        loguru.logger.info(f'Marking run as finished in cache')
        cache.update_cache(data)
        return False

