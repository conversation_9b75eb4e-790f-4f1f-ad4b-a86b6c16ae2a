name: PR-Build

on: pull_request_target

jobs:
  docker-build:
    runs-on: eg-default
    env:
      IMAGE_TAG: ${{ github.sha }}
      EG_ARTY_USER: ${{ secrets.EG_ARTY_USER }}
      EG_ARTY_PASSWORD: ${{ secrets.EG_ARTY_PASSWORD }}
      RELEASE_REGISTRY: eg-docker-release-local.artylab.expedia.biz

    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          ref: "refs/pull/${{ github.event.number }}/merge"

      - name: Docker Login to docker release registry
        uses: actions/docker-login-action@v1
        with:
          registry: ${{ env.RELEASE_REGISTRY }}
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}

      - name: Docker Registry Login - hub-docker-remote
        uses: actions/docker-login-action@v2
        with:
          registry: hub-docker-remote.artylab.expedia.biz
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}

      - name: Build Image
        run: |
          make docker_build IMAGE_TAG=$IMAGE_TAG EG_ARTY_USER=$EG_ARTY_USER EG_ARTY_PASSWORD=$EG_ARTY_PASSWORD

      - name: Run Pytests
        run: |
          make docker_test IMAGE_TAG=$IMAGE_TAG EG_ARTY_USER=$EG_ARTY_USER EG_ARTY_PASSWORD=$EG_ARTY_PASSWORD