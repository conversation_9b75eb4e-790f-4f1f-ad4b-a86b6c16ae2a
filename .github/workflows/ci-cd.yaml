name: CI-CD
on:
  push:
    branches:
      - main
jobs:
  build-publish:
    runs-on: eg-default
    env:
      IMAGE_TAG: ${{ github.sha }}
      EG_ARTY_USER: ${{ secrets.EG_ARTY_USER }}
      EG_ARTY_PASSWORD: ${{ secrets.EG_ARTY_PASSWORD }}
      RELEASE_REGISTRY: eg-docker-release-local.artylab.expedia.biz
      CHANNEL: google-hotel-ads
      REPO_NAME: mbt

    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}

      - name: Docker Login to docker release registry
        uses: actions/docker-login-action@v1
        with:
          registry: ${{ env.RELEASE_REGISTRY }}
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}

      # Login to hub-docker-remote shouldn't be required, but we do it to avoid intermittent errors
      # https://confluence.expedia.biz/pages/viewpage.action?spaceKey=DSK&title=Enterprise+runner+default+Artifactory+credentials
      - name: Docker Registry Login - hub-docker-remote
        uses: actions/docker-login-action@v2
        with:
          registry: hub-docker-remote.artylab.expedia.biz
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}

      - name: Build Image
        run: |
          make docker_build IMAGE_TAG=$IMAGE_TAG EG_ARTY_USER=$EG_ARTY_USER EG_ARTY_PASSWORD=$EG_ARTY_PASSWORD

      - name: Push Image
        run: |
          make docker_push IMAGE_TAG=$IMAGE_TAG EG_ARTY_USER=$EG_ARTY_USER EG_ARTY_PASSWORD=$EG_ARTY_PASSWORD

  deploy-test:
    runs-on: eg-default
    needs: build-publish
    env:
      IMAGE_TAG: ${{ github.sha }}
      ENV: test
      CHANNEL: google-hotel-ads
      REPO_NAME: mbt
    steps:
      - name: Checkout Gitops
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          repository: ${{ env.GITOPS_REPO }}
          fetch-depth: 0
      - name: Deploy to marketing cluster stage
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/$CHANNEL/$REPO_NAME
          image-tag: ${{ github.sha }}
          environment: 'test'

      - name: Deploy TA reference to stage
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/trip-advisor/mbt
          image-tag: ${{ github.sha }}
          environment: 'test'

      - name: Deploy Kayak reference to stage
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/kayak/mbt
          image-tag: ${{ github.sha }}
          environment: 'test'

  deploy-prod:
    runs-on: eg-default
    environment: 'prod'
    needs: deploy-test
    env:
      IMAGE_TAG: ${{ github.sha }}
      ENV: prod
      CHANNEL: google-hotel-ads
      REPO_NAME: mbt
    steps:
      - name: Checkout Gitops
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          repository: ${{ env.GITOPS_REPO }}
          fetch-depth: 0
      - name: Deploy to marketing cluster prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/$CHANNEL/$REPO_NAME
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy to marketing cluster prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/bing-search/$REPO_NAME
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy TA reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/trip-advisor/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy Kayak reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/kayak/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy Trivago reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/trivago/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy Hometogo reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/hometogo/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy VacationRenter reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/vacation-renter/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy Skyscanner reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/skyscanner/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'

      - name: Deploy Cheapflights reference to prod
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: ottoman/algorithms/cheap-flights/mbt
          image-tag: ${{ github.sha }}
          environment: 'prod'