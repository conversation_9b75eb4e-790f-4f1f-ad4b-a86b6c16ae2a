---
name: GHAS-CodeQL-Scan

on:
  workflow_dispatch:
  push:
    branches: [master, main]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [master, main]
  schedule:
    - cron: '13 21 * * 1'

jobs:
  analyze:
    name: Analyze
    runs-on: [eg-securityscan]
    strategy:
      fail-fast: false
      matrix:
        language: [python]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - name: Install Python dependencies
        if: matrix.language == 'python'
        run: |
          python -m pip install --upgrade pip
          pip install  -r ./requirements.txt 
          echo "CODEQL_PYTHON=$(which python)" >> $GITHUB_ENV
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-extended
          # ℹ️ CodeQL will attempt to auto-install Python dependencies. This
          # behavior must be disabled when steps 'Setup Python' and
          # 'Install Python dependencies' are included in the workflow.
          setup-python-dependencies: false
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
