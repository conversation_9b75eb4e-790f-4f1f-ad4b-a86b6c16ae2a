# ottoman self serve template

## Compile & Run

**Install Docker**

https://docs.docker.com/engine/install/

Build:

`docker compose build`

Run: `docker compose run workflow python3 /app/src/executor.py --runId test --parameters '{}' --channel google-search --inputS3Path s3://eg-sem-eng-compute-test/algo-test/`

Note: If you encounter the error during `docker compose build`. Just create an empty workspace folder `mkdir ./workspace`

## support for versions

If the code repo supports different versions of algo, version will come as part of
parameters

if version field in empty, it will run default, or it will run the version provided from
parameters

so enable version for algo it should be included in run_parameters_schema when you define
or on-board algo on to ottoman

To include version parameters into algo parameters schema, use
this [link](https://galaxy.marketing.expedia.com/ottoman/algorithm) and if this is a
scheduled algorithm, update in schedule tab as
well: [link](https://galaxy.marketing.expedia.com/ottoman/schedule)

## importing your code

After you create a repo using this template, you can commit your code
under `/src/default/algo/__init__.py`
In init, run function is called from executor, so you can commit your code under this
function, unless if you want to create your version of algo other than default commit your
code to `/src/version/algo/__init__.py`
Entrypoint is src/executor.py, from this you have to call your code that you want to run

## ottoman on-boarding

look at the on-boarding docs to understand more on what is the input, outputs after your
algo is run.

[on-boarding](https://github.expedia.biz/gmo-performance-marketing/ottoman)


