version: "3"

services:
  workflow:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        - "EG_ARTY_USER=${EG_ARTY_USER}"
        - "EG_ARTY_PASSWORD=${EG_ARTY_PASSWORD}"
    volumes:
      - ./:/app
      - $HOME/.aws:/root/.aws:ro
    env_file:
      - ./dev.env
  dragonfly:
    image: docker.dragonflydb.io/dragonflydb/dragonfly
    ulimits:
      memlock: -1
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "--raw", "incr", "ping" ]
      interval: 10ms
      retries: 100
