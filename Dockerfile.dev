FROM apache/spark-py:v3.4.0

WORKDIR /app

ARG EG_ARTY_USER
ARG EG_ARTY_PASSWORD

COPY requirements.txt .

ENV PYTHONPATH=$PYTHONPATH:/app/src
USER root
RUN apt-get update && apt-get install -y python3 && apt-get install -y \
    python3-pip

RUN python3 -m pip install --no-cache -r requirements.txt --extra-index-url "https://$EG_ARTY_USER:$<EMAIL>/api/pypi/eg-pypi-release-local/simple"

COPY ./workspace /workspace